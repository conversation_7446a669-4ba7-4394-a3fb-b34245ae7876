<template>
  <div class="patient-info-bar">
    <el-card shadow="never" class="info-card">
      <div class="info-content">
        <div class="info-item">
          <span class="label">病人ID:</span>
          <span class="value">{{ mypatientinfo.patientId }}</span>
        </div>
        <div class="info-item">
          <span class="label">住院号:</span>
          <span class="value">{{ mypatientinfo.hospitalNumber }}</span>
        </div>
        <div class="info-item">
          <span class="label">床号:</span>
          <span class="value">{{ mypatientinfo.ward }}</span>
        </div>
        <div class="info-item">
          <span class="label">身高/体重:</span>
          <span class="value">{{ mypatientinfo.height }}cm / {{ mypatientinfo.weight }}kg</span>
        </div>
        <div class="info-item highlight diagnosis">
          <span class="label">诊断:</span>
          <span class="value">{{ mypatientinfo.diagnosis }}</span>
        </div>
        <div class="info-item highlight allergy">
          <span class="label">过敏药物:</span>
          <span class="value">{{ mypatientinfo.mbm || '无' }}</span>
        </div>
        <div class="info-item highlight nursing">
          <span class="label">护理等级:</span>
          <span class="value">{{ mypatientinfo.nursingLevel }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  patientinfo: {
    type: Object,
    default: () => ({
      patientId: '',
      hospitalNumber: '',
      height: 0,
      weight: 0,
      diagnosis: '',
      mbm: '',
      ward: '',
      nursingLevel: ''
    })
  }
})

const mypatientinfo = computed(() => {
  return props.patientinfo;
})
</script>

<style scoped>
.patient-info-bar {
  margin: 0;
}

.info-card {
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  padding: 0;
}

.info-card :deep(.el-card__body) {
  padding: 8px 12px;
}

.info-content {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  overflow-x: auto;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 3px;
  white-space: nowrap;
  flex-shrink: 0;
}

.label {
  color: #606266;
  margin-right: 4px;
  font-weight: 600;
  min-width: auto;
}

.value {
  color: #303133;
  font-weight: 500;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 突出显示的项目 */
.highlight {
  border-left: 2px solid;
}

.diagnosis {
  border-left-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.diagnosis .label {
  color: #409EFF;
}

.allergy {
  border-left-color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

.allergy .label {
  color: #F56C6C;
}

.nursing {
  border-left-color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.nursing .label {
  color: #67C23A;
}

/* 响应式设计 - 在非常小的屏幕上允许滚动 */
@media (max-width: 768px) {
  .info-content {
    overflow-x: auto;
    padding-bottom: 4px; /* 为滚动条留出空间 */
  }
}
</style>