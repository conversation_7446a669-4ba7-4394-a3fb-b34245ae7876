<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.397'  />
					<Space count='10' />
					<Font size='0.767' cfg='1' />死亡病例讨论记录</Paragraph>
				<Paragraph>
					<Font size='0.397' cfg='0' />
					<Space count='14' />
					<Font size='0.370' cfg='1' />Mortality<Space />Conference<Space />Minute</Paragraph>
				<Paragraph>
					<Separator height='0.123' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' cfg='0' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住  院  科  室  名  称'>
						<Hint cfg='1000'>
							<Font color='808080' />住<Space count='2' />院<Space count='2' />科<Space count='2' />室<Space count='2' />名<Space count='2' />称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />H01-06-YW-018<Space count='49' />第<PageNum width='0.481' height='0.469' lCfg='2'>
						<Unit width='0.481' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='3' />共<PageNum xCfg='1' width='0.395' height='0.469' lCfg='2'>
						<Unit width='0.395' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='61' />2014-03-A1</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='14' cols='2' padding='0.012'>
						<Row height='0.560'>
							<Cell xCfg='1' eCfg='80' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />时<Space count='4' />间：<Font size='0.397' cfg='0' />
									<Element cfg='30000' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />地<Space count='4' />点：<Font cfg='0' />
									<Element cfg='30000' hint='地点'>
										<Hint cfg='1000'>
											<Font color='808080' />地点</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />参加人员：<Font cfg='0' />
									<Element name='SOAPCurSymIN' cfg='30000' hint='（全名及专业技术职称）'>
										<Hint cfg='1000'>
											<Font color='808080' />（全名及专业技术职称）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />主<Space />持<Space />人：<Font cfg='0' />
									<Element cfg='30000' hint='（全名及专业技术职称）'>
										<Hint cfg='1000'>
											<Font color='808080' />（全名及专业技术职称）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />病例报告人：<Font cfg='0' />
									<Element cfg='30000' hint='病历报告人'>
										<Hint cfg='1000'>
											<Font color='808080' />病历报告人</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />病情摘要：<Font cfg='0' />
									<Element cfg='30000' hint='（姓名、性别、年龄、病情简介并提出需要讨论的问题）'>
										<Hint cfg='1000'>
											<Font color='808080' />（姓名、性别、年龄、病情简介并提出需要讨论的问题）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />发<Space />言<Space />人：<Font cfg='0' />
									<Element name='EMRPtFamilyHistory' cfg='30000' hint='（全名及专业技术职称）'>
										<Hint cfg='1000'>
											<Font color='808080' />（全名及专业技术职称）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />主持人总结意见：<Font cfg='0' />
									<Element name='SOAPMediExamIN' cfg='30000' hint='主持人总结意见'>
										<Hint cfg='1000'>
											<Font color='808080' />主持人总结意见</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />最后诊断:<Font cfg='0' />
									<Element cfg='30000' hint='最后诊断' borderStyle='1' borderWidth='0.012'>
										<Hint cfg='1000'>
											<Font color='808080' />最后诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />死亡原因:<Font cfg='0' />
									<Element cfg='30000' hint='死亡原因' borderStyle='1' borderWidth='0.012'>
										<Hint cfg='1000'>
											<Font color='808080' />死亡原因</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />经验教训:<Font cfg='0' />
									<Element cfg='30000' hint='经验教训' borderStyle='1' borderWidth='0.012'>
										<Hint cfg='1000'>
											<Font color='808080' />经验教训</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />主持人签名/工号：<Font cfg='404' />
									<Space count='16' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />记录人签名/工号：<Font cfg='404' />
									<Space count='16' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.442' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='10' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分' beforeTag='日    期：'>
										<BeforeTag cfg='4'>
											<Font color='ff' />日期：</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
