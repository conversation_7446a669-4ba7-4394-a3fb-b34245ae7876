<template>
  <div>
    <div class="experiment">
      <div class="bars">
        <!-- 使用kebab-case命名法 -->
        <vue-file-toolbar-menu v-for="(content, index) in bars_content" :key="'bar-' + index" :content="content" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { VueFileToolbarMenu } from 'vue-file-toolbar-menu';
import { editorsInstanceManager } from '@/components/editorHelper/ThinkEditorInstance.Manager';
import { ThinkEditorDemo } from './ThinkEditorDemo';
import {
  fontsManager,
  editorLogger,
  E_DISPLAY_MODE,
  E_EVENT_KEY,
  E_PAGE_NUM_SCOPE,
  E_PAGE_NUM_STYLE,
  E_PAGE_NUM_FORMAT,
  E_CHECK_FIGURE_STYLE,
  E_VIEW_MODE,
  E_FORMULA_STYLE,
  E_LAYOUT_VERTICAL_ALIGN,
  E_BRUSH_STATE,
  E_PASTE_TYPE,
  E_ALIGN_HORIZONTAL_MODE,
  E_SCRIPT_MODE
} from '@/components/editor/ThinkEditor.Defined';
import { Color2RGBAStr } from '@/components/editor/ThinkEditor.Utils';

// 定义emit
const emit = defineEmits(['docOpt', 'closeDocEditor', 'closeAllDocEditor', 'addEditorInstance', 'removeEditorInstance']);

// 响应式状态
const initial_html = ref('Powered by ThinkEditor!');
const font = ref('宋体');
const font_color = ref('rgb(0, 0, 0)');
const font_size = ref(0.4); // unit: cm
const theme = ref('default');
const edit_mode = ref(true);
const check1 = ref(false);
const check2 = ref(false);
const check3 = ref(true);
const bold_state_ = ref(false);
const italic_state_ = ref(false);
const strikethrough_state_ = ref(false);
const underline_state_ = ref(false);
const thinkEditor = ref();
const thinkEditorDemo = ref();
const fontNameList = ref([]);
const script = ref(0);
const ferrule = ref(0);
const font_weight = ref('常规');
const fontSizeItem = ref('五号');
const font_list = ref(['宋体', '楷体', '黑体', '仿宋', 'Garamond', 'Arial', 'Avenir', 'Courier New', 'Garamond', 'Georgia', 'Impact', 'Helvetica', 'Palatino', 'Roboto', 'Times New Roman', 'Verdana']);

// 计算属性
const isMacLike = computed(() => {
  return /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);
});

const is_touch_device = computed(() => {
  return 'ontouchstart' in window || window.navigator.msMaxTouchPoints > 0;
});

const my_menu_one = computed(() => {
  return [
    {
      icon: 'format_clear',
      title: '清除格式',
      click: () => thinkEditor.value?.ClearFormat()
    },
    {
      icon: 'format_paint',
      title: '格式刷',
      click: () => thinkEditor.value?.SetFormatBrush(E_BRUSH_STATE.BrushOnce)
    },
    {
      icon: 'undo',
      title: '恢复',
      click: () => thinkEditor.value?.Undo(1)
    },
    {
      icon: 'redo',
      title: '撤销',
      click: () => thinkEditor.value?.Redo(1)
    },
    {
      icon: 'file_copy',
      title: '复制',
      click: () => thinkEditor.value?.Copy()
    },
    {
      icon: 'content_cut',
      title: '剪切',
      click: () => thinkEditor.value?.Cut()
    },
    {
      icon: 'content_copy',
      title: '粘贴',
      click: () => thinkEditor.value?.Paste(E_PASTE_TYPE.Normal)
    }
  ];
});

const font_menu = computed(() => {
  return fontNameList.value.map(font => {
    return {
      html: '<span class="ellipsis" style="font-family:' + font + '">' + font + '</span>',
      icon: theme.value != 'default' && font.value == font ? 'check' : false,
      active: font.value == font,
      height: 20,
      click: () => {
        thinkEditor.value?.SetFontType(font);
        font.value = font;
      }
    };
  });
});

const font_glyph_menu = computed(() => {
  // 实现字体粗细菜单
  return [];
});

const font_size_menu = computed(() => {
  // 实现字体大小菜单
  return [];
});

const my_menu_two = computed(() => {
  return [
    {
      html: '<div class="ellipsis" style="width: 80px; font-size: 95%;">' + font.value + '</div>',
      title: 'Font',
      chevron: true,
      menu: font_menu.value,
      menu_height: 200,
      click: async () => {
        fontNameList.value = await fontsManager.GetfontsNameList();
        fontNameList.value.unshift('宋体', '黑体', '楷体', '仿宋');
      }
    },
    { is: 'separator' },
    {
      html: '<div class="ellipsis" style="width: 60px; font-size: 95%;">' + font_weight.value + '</div>',
      title: 'font-weight',
      chevron: true,
      menu: font_glyph_menu.value,
      menu_height: 200,
      menu_width: 100
    },
    { is: 'separator' },
    {
      icon: 'format_bold',
      title: '加粗',
      active: bold_state_.value,
      click: () => {
        bold_state_.value = !bold_state_.value;
        thinkEditor.value?.SetFontBold(bold_state_.value);
      }
    },
    {
      icon: 'format_italic',
      title: '斜体',
      active: italic_state_.value,
      click: () => {
        italic_state_.value = !italic_state_.value;
        thinkEditor.value?.SetFontItalic(italic_state_.value);
      }
    },
    {
      icon: 'format_underline',
      title: '下划线',
      active: underline_state_.value,
      click: () => {
        underline_state_.value = !underline_state_.value;
        thinkEditor.value?.SetFontUnderline(underline_state_.value);
      }
    },
    {
      icon: 'format_strikethrough',
      title: '删除线',
      active: strikethrough_state_.value,
      click: () => {
        strikethrough_state_.value = !strikethrough_state_.value;
        thinkEditor.value?.SetFontStrikethrough(strikethrough_state_.value);
      }
    },
    {
      icon: 'format_color_text',
      title: '字体颜色',
      menu: [
        {
          html: '<div style="width: 120px; display: flex; flex-wrap: wrap; justify-content: space-between;">' +
            ['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#00FFFF', '#FF00FF', '#C0C0C0', '#FFFFFF'].map(color => {
              return '<div style="width: 20px; height: 20px; margin: 2px; background-color: ' + color + '; border: 1px solid #ccc;"></div>';
            }).join('') +
            '</div>',
          click: (e) => {
            const target = e.target;
            if (target.style && target.style.backgroundColor) {
              const color = target.style.backgroundColor;
              thinkEditor.value?.SetFontColor(color);
              font_color.value = color;
            }
          }
        }
      ],
      menu_height: 80
    },
    {
      icon: 'format_align_left',
      title: '左对齐',
      click: () => thinkEditor.value?.SetParagraphAlign(E_ALIGN_HORIZONTAL_MODE.Left)
    },
    {
      icon: 'format_align_center',
      title: '居中对齐',
      click: () => thinkEditor.value?.SetParagraphAlign(E_ALIGN_HORIZONTAL_MODE.Center)
    },
    {
      icon: 'format_align_right',
      title: '右对齐',
      click: () => thinkEditor.value?.SetParagraphAlign(E_ALIGN_HORIZONTAL_MODE.Right)
    },
    {
      icon: 'format_align_justify',
      title: '两端对齐',
      click: () => thinkEditor.value?.SetParagraphAlign(E_ALIGN_HORIZONTAL_MODE.Justify)
    }
  ];
});

// 菜单内容
const bars_content = ref([
  my_menu_one,
  my_menu_two
]);

// 生命周期钩子
onMounted(() => {
  // 初始化操作
});

// 方法
const bind = (editor) => {
  thinkEditor.value = editor;

  // 监听编辑器事件
  if (thinkEditor.value) {
    thinkEditor.value.addEventListener(E_EVENT_KEY.fontFormat, (e) => {
      const data = e.data;
      if (data.fontBold !== undefined) {
        bold_state_.value = data.fontBold;
      }
      if (data.fontItalic !== undefined) {
        italic_state_.value = data.fontItalic;
      }
      if (data.fontUnderline !== undefined) {
        underline_state_.value = data.fontUnderline;
      }
      if (data.fontStrikethrough !== undefined) {
        strikethrough_state_.value = data.fontStrikethrough;
      }
      if (data.fontType !== undefined) {
        font.value = data.fontType;
      }
      if (data.fontSize !== undefined) {
        font_size.value = data.fontSize;
      }
      if (data.fontColor !== undefined) {
        font_color.value = Color2RGBAStr(data.fontColor);
      }
      if (data.script !== undefined) {
        script.value = data.script;
      }
    });
  }
};

// 暴露方法给父组件
defineExpose({
  bind
});
</script>

<style scoped>
.experiment {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bars {
  display: flex;
  flex-direction: column;
}

.bars>* {
  margin-bottom: 5px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>