<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.000' height='29.700' borderWidth='0.020' topPadding='2.540' bottomPadding='2.540' leftPadding='3.140' rightPadding='3.140'>
			<Attach backColor='ffffff' />
			<Header topMargin='0.300' topBorderWidth='0.020' bottomBorderWidth='0.100' leftBorderWidth='0.020' rightBorderWidth='0.020'>
				<Paragraph spaceAfter='0.100' lineSpaceValue='0.600' />
			</Header>
			<Footer bottomMargin='0.300'>
				<Paragraph lineSpaceValue='0.600' />
			</Footer>
			<Body borderWidth='0.030'>
				<Paragraph>当前患者：<Element cfg='30000' inputMode='2' optionClass='医生' optionId='当前患者' hint='选择患者'>
						<Hint cfg='1000'>
							<Font color='808080' />选择<SelectionBegin side='2' />
							<SelectionEnd side='2' />患者</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph />
				<Paragraph>1、知识库中制作选项：<Space />“医生”-“今日患者”</Paragraph>
				<Paragraph>2、插入或修改元素属性：配置-输入模式-选项-选择<Space />选项类（医生）选项组（今日患者）</Paragraph>
				<Paragraph>3、双击“选择患者”元素，编辑器产生elementResource.request事件</Paragraph>
				<Paragraph>4、应用监听编辑器elementResource.request事件，根据消息体中元素信息知道该元素需要“今日患者”可选项</Paragraph>
				<Paragraph>应用调用接口SetOptions()设置或覆盖编辑器的选项</Paragraph>
				<Paragraph>5、UI使用最新设置，动态展示选项供医生选择输入</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes>
			<Options class='医生'>
				<Option id='当前患者' />
			</Options>
		</OptionRes>
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
