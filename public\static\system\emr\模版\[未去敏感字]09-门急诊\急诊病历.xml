<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='1' width='21.008' height='29.713' padding='2.438'>
			<Attach backColor='ffffff' />
			<Header>
				<Paragraph />
				<Paragraph />
				<Paragraph xCfg='2'>
					<Font size='0.503' fontName='黑体' backColor='ffffff' cfg='1' />急诊病历</Paragraph>
				<Paragraph spaceAfter='0.080'>
					<Table rows='2' cols='4' padding='0.012'>
						<Row height='0.801'>
							<Cell xCfg='1' eCfg='1' width='4.488' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />病人ID号:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EME_PID' cfg='30000' hint='病人ID'>
										<Hint cfg='1000'>
											<Font color='808080' />病人ID</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='4.353' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />姓<Space count='4' />名:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EME_NAME' cfg='30000' hint='病人姓名'>
										<Hint cfg='1000'>
											<Font color='808080' />病人姓名</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='3.798' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />性<Space count='4' />别:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EME_SEX' cfg='30000' hint='性别'>
										<Hint cfg='1000'>
											<Font color='808080' />性别</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='3.267' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />年<Space count='4' />龄:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EME_AGE' cfg='30000' hint='年龄'>
										<Hint cfg='1000'>
											<Font color='808080' />年龄</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.801'>
							<Cell xCfg='1' eCfg='1' width='4.488' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />病<Space />历<Space />号:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EMR_RegisterNo' cfg='30000' inputMode='2' hint='病历号'>
										<Hint cfg='1000'>
											<Font color='808080' />病历号</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='4.353' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />来诊方式:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EME_TreatmentType' cfg='30000' hint='来诊方式'>
										<Hint cfg='1000'>
											<Font color='808080' />来诊方式</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='3.798' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />就诊区域:<Font cfg='0' />
									<Element name='DIC_EMC_EMR.EMR_District' cfg='30000' inputMode='2' hint='就诊区域'>
										<Hint cfg='1000'>
											<Font color='808080' />就诊区域</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='1' width='3.267' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph />
							</Cell>
						</Row>
					</Table>
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph />
				<Paragraph xCfg='2'>
					<Font color='808080' />第<PageNum width='0.826' height='0.506' lCfg='2'>
						<Unit eCfg='1' width='0.826' height='0.506'>
							<Paragraph xCfg='2'>
								<Font color='808080' />
							</Paragraph>
						</Unit>
					</PageNum>页<Space />共<PageNum xCfg='1' width='0.851' height='0.432' lCfg='2'>
						<Unit eCfg='1' width='0.851' height='0.432'>
							<Paragraph xCfg='2'>
								<Font color='808080' />
							</Paragraph>
						</Unit>
					</PageNum>页</Paragraph>
				<Paragraph xCfg='0'>
					<Font size='0.265' color='0' backColor='ffffff' />沈阳军区总医院急诊医学部<Space />Emergency<Space />Medicine<Space />Department<Space />of<Space />General<Space />Hospital<Space />of<Space />Shenyang<Space />Military<Space />Command<Font backColor='ff000000' />
					<Space count='2' />
				</Paragraph>
				<Paragraph>全军重症(战)创伤救治中心<Space />Rescue<Space />Center<Space />of<Space />Severe<Space />Wound<Space />and<Space />Trauma.<Space />PLA</Paragraph>
				<Paragraph>地址：沈阳市沈河区文化路83号<Space />No.83,<Space />wenhua<Space />road,shenhe<Space />district,shenyang<Space />
				</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='2' spaceAfter='0.080'>
					<Table rows='4' cols='1' padding='0.012'>
						<Row height='0.800'>
							<Cell xCfg='1' width='15.911' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />主<Space count='2' />诉：<Font cfg='0' />
									<Element name='主诉' cfg='30000' hint='主诉'>
										<Hint cfg='1000'>
											<Font color='808080' />主诉</Hint>
									</Element>
									<Font color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.800'>
							<Cell xCfg='1' width='15.911' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />现病史：<Font cfg='0' />患者于<Element cfg='30000' hint='XXXX'>
										<Hint cfg='1000'>
											<Font color='808080' />XXXX</Hint>
									</Element>
									<Font color='0' />年<Element cfg='30000' hint='XX'>
										<Hint cfg='1000'>
											<Font color='808080' />XX</Hint>
									</Element>
									<Font color='0' />月<Element cfg='30000' hint='XX'>
										<Hint cfg='1000'>
											<Font color='808080' />XX</Hint>
									</Element>
									<Font color='0' />日<Element name='发病症状' cfg='30000' hint='发病症状'>
										<Hint cfg='1000'>
											<Font color='808080' />发病症状</Hint>
									</Element>
									<Font color='0' />，<Element name='伴随症状' cfg='30000' hint='伴随症状'>
										<Hint cfg='1000'>
											<Font color='808080' />伴随症状</Hint>
									</Element>
									<Font color='0' />，<Element name='阴性症状' cfg='30000' hint='阴性症状'>
										<Hint cfg='1000'>
											<Font color='808080' />阴性症状</Hint>
									</Element>
									<Font color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.800'>
							<Cell xCfg='1' width='15.911' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />既往史：<CheckBox id='checkbox9' xCfg='100'>
										<Font cfg='0' />
										<CF />高血压</CheckBox>
									<Space />
									<CheckBox id='checkbox10' xCfg='100'>
										<CF />冠心病</CheckBox>
									<Space />
									<CheckBox id='checkbox11' xCfg='100'>
										<CF />糖尿病</CheckBox>
									<Space />
									<CheckBox id='checkbox12' xCfg='100'>
										<CF />COPD</CheckBox>
									<Space />
									<CheckBox id='checkbox8' xCfg='100'>
										<CF />脑血管病</CheckBox>
									<Space />
									<CheckBox id='checkbox16' xCfg='100'>
										<CF />慢性支气管炎</CheckBox>
									<Space />
									<CheckBox id='checkbox17' xCfg='100'>
										<CF />哮喘</CheckBox>
								</Paragraph>
								<Paragraph>
									<Space count='8' />
									<CheckBox id='checkbox18' xCfg='100'>
										<CF />支气管扩张症</CheckBox>
									<Space />
									<CheckBox id='checkbox19' xCfg='100'>
										<CF />肝脏疾病</CheckBox>
									<Space />
									<CheckBox id='checkbox20' xCfg='100'>
										<CF />肾脏疾病</CheckBox>
									<Space />
									<CheckBox id='checkbox21' xCfg='100'>
										<CF />肿瘤</CheckBox>
									<Space />
									<CheckBox id='checkbox22' xCfg='100'>
										<CF />吸烟</CheckBox>
									<Space />
									<CheckBox id='checkbox23' xCfg='100'>
										<CF />饮酒</CheckBox>
								</Paragraph>
								<Paragraph>
									<Space count='7' />
									<Element name='病人既往史' cfg='30000' hint='病人既往史'>
										<Hint cfg='1000'>
											<Font color='808080' />病人既往史</Hint>
									</Element>
									<Font color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.800'>
							<Cell xCfg='1' width='15.911' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />过敏史<Font cfg='0' />:<CheckBox id='checkbox13' xCfg='100'>
										<CF />药物</CheckBox>
									<Element name='过敏药物' cfg='30000' inputMode='2' optionClass='system' optionId='过敏药物' hint='过敏药物'>
										<Hint cfg='1000'>
											<Font color='808080' />过敏药物</Hint>
									</Element>
									<Font color='0' />
									<Space count='3' />
									<CheckBox id='checkbox14' xCfg='100'>
										<CF />食物</CheckBox>
									<Space />
									<Element name='过敏食物' cfg='30000' inputMode='2' optionClass='system' optionId='过敏食物' hint='过敏食物'>
										<Hint cfg='1000'>
											<Font color='808080' />过敏食物</Hint>
									</Element>
									<Font color='0' />
									<Space count='2' />
									<CheckBox id='checkbox15' xCfg='100'>
										<CF />其他</CheckBox>
									<Space />
									<Element name='其他' cfg='30000' hint='其他'>
										<Hint cfg='1000'>
											<Font color='808080' />其他</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
					<Font cfg='1' />体格检查</Paragraph>
				<Paragraph spaceAfter='0.080'>
					<Table rows='1' cols='5' padding='0.012'>
						<Row height='0.800'>
							<Cell xCfg='1' width='2.343' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />T：<Font cfg='0' />
									<Element name='EMR_VitalSign.Temp' cfg='30000' hint='体温'>
										<Hint cfg='1000'>
											<Font color='808080' />体温</Hint>
									</Element>
									<Font color='0' />
									<Space />
									<Font cfg='1' />℃</Paragraph>
							</Cell>
							<Cell xCfg='1' width='3.815' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />BP:<Space />
									<Font cfg='0' />
									<Element name='EMR_VitalSign.SBP' cfg='30000' hint='SBP'>
										<Hint cfg='1000'>
											<Font color='808080' />SBP</Hint>
									</Element>
									<Font color='0' />/<Element name='EMR_VitalSign.DBP' cfg='30000' hint='DBP'>
										<Hint cfg='1000'>
											<Font color='808080' />DBP</Hint>
									</Element>
									<Font color='0' cfg='1' />
									<Space />mmHg</Paragraph>
							</Cell>
							<Cell xCfg='1' width='2.690' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />P:<Font cfg='0' />
									<Element name='EMR_VitalSign.MaiBo' cfg='30000' hint='脉搏'>
										<Hint cfg='1000'>
											<Font color='808080' />脉搏</Hint>
									</Element>
									<Font color='0' cfg='1' />
									<Space />BPM</Paragraph>
							</Cell>
							<Cell xCfg='1' width='3.011' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />R<Space />:<Font cfg='0' />
									<Element name='EMR_VitalSign.Huxi' cfg='30000' hint='呼吸'>
										<Hint cfg='1000'>
											<Font color='808080' />呼吸</Hint>
									</Element>
									<Font color='0' cfg='1' />
									<Space />BPM</Paragraph>
							</Cell>
							<Cell xCfg='1' width='3.899' borderWidth='0.012' borderStyle='1' bottomPadding='0.120' leftPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />瞳孔:L<Font cfg='0' />
									<Space />
									<Element name='EMR_VitalSign.LeftEyeSize' cfg='30000' hint='L'>
										<Hint cfg='1000'>
											<Font color='808080' />L</Hint>
									</Element>
									<Font color='0' />
									<Space />
									<Font cfg='1' />R<Space />
									<Font cfg='0' />
									<Element name='EMR_VitalSign.RightEyeSize' cfg='30000' hint='R'>
										<Hint cfg='1000'>
											<Font color='808080' />R</Hint>
									</Element>
									<Font color='0' />
									<Space />
									<Font cfg='1' />mm</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph spaceAfter='0.080'>
					<Table id='table4' rows='1' cols='1' padding='0.012'>
						<Row height='0.800'>
							<Cell xCfg='1' width='15.851' borderWidth='0.012' borderStyle='1' topPadding='0.080' bottomPadding='0.080' leftPadding='0.120' rightPadding='0.120'>
								<Paragraph>
									<Font size='0.370' />患者<Element name='神志情况' cfg='30000' inputMode='2' optionClass='system' optionId='神志情况' hint='神志情况'>
										<Hint cfg='1000'>
											<Font color='808080' />神志情况</Hint>
									</Element>
									<Font color='0' />，<Element name='言语' cfg='30000' inputMode='2' optionClass='system' optionId='言语' hint='言语'>
										<Hint cfg='1000'>
											<Font color='808080' />言语</Hint>
									</Element>
									<Font color='0' />，<Element name='体位' cfg='30000' inputMode='2' optionClass='system' optionId='体位' hint='体位'>
										<Hint cfg='1000'>
											<Font color='808080' />体位</Hint>
									</Element>
									<Font color='0' />体位，<Element name='皮肤情况' cfg='30000' inputMode='2' optionClass='system' optionId='皮肤情况' hint='皮肤情况'>
										<Hint cfg='1000'>
											<Font color='808080' />皮肤情况</Hint>
									</Element>
									<Font color='0' />，查体<Element name='配合检查' cfg='30000' inputMode='2' optionClass='system' optionId='配合检查' hint='配合检查'>
										<Hint cfg='1000'>
											<Font color='808080' />配合检查</Hint>
									</Element>
									<Font color='0' />，<Element name='巩膜情况' cfg='30000' inputMode='2' optionClass='system' optionId='巩膜情况' hint='巩膜情况'>
										<Hint cfg='1000'>
											<Font color='808080' />巩膜情况</Hint>
									</Element>
									<Font color='0' />，<Element name='结膜情况' cfg='30000' inputMode='2' optionClass='system' optionId='结膜情况' hint='结膜情况'>
										<Hint cfg='1000'>
											<Font color='808080' />结膜情况</Hint>
									</Element>
									<Font color='0' />，<Element name='口唇情况' cfg='30000' inputMode='2' optionClass='system' optionId='口唇情况' hint='口唇情况'>
										<Hint cfg='1000'>
											<Font color='808080' />口唇情况</Hint>
									</Element>
									<Font color='0' />，颈静脉<Element name='颈静脉' cfg='30000' inputMode='2' optionClass='system' optionId='颈静脉' hint='颈静脉'>
										<Hint cfg='1000'>
											<Font color='808080' />颈静脉</Hint>
									</Element>
									<Font color='0' />。<Element name='胸廓间接压痛' cfg='30000' inputMode='2' optionClass='system' optionId='胸廓间接压痛' hint='胸廓间接压痛'>
										<Hint cfg='1000'>
											<Font color='808080' />胸廓间接压痛</Hint>
									</Element>
									<Font color='0' />
									<Element cfg='30000' hint='干湿罗音'>
										<Element name='心肺整体情况' cfg='30000' inputMode='2' optionClass='system' optionId='心肺整体情况' hint='，'>，</Element>
									</Element>
									<Element cfg='30000' hint='  '>
										<Element name='呼吸情况' cfg='30000' inputMode='2' optionClass='system' optionId='呼吸情况' hint='呼吸情况'>
											<Hint cfg='1000'>
												<Font color='808080' />呼吸情况</Hint>
										</Element>
										<Font color='0' />，<Element name='肺呼吸音' cfg='30000' inputMode='2' optionClass='system' optionId='肺呼吸音' hint='肺呼吸音'>
											<Hint cfg='1000'>
												<Font color='808080' />肺呼吸音</Hint>
										</Element>
										<Font color='0' />，<Element name='干湿啰音' cfg='30000' inputMode='2' optionClass='system' optionId='干湿啰音' hint='干湿啰音'>
											<Hint cfg='1000'>
												<Font color='808080' />干湿啰音</Hint>
										</Element>
										<Font color='0' />，心率<Element cfg='30000' hint='次数'>
											<Hint cfg='1000'>
												<Font color='808080' />次数</Hint>
										</Element>
										<Font color='0' />次/分，心律<Element name='心律情况' cfg='30000' inputMode='2' optionClass='system' optionId='心律情况' hint='心律情况'>
											<Hint cfg='1000'>
												<Font color='808080' />心律情况</Hint>
										</Element>
										<Font color='0' />。</Element>全腹<Element name='全腹情况' cfg='30000' inputMode='2' optionClass='system' optionId='全腹情况' hint='全腹情况'>
										<Hint cfg='1000'>
											<Font color='808080' />全腹情况</Hint>
									</Element>
									<Font color='0' />，<Element name='腹部压痛' cfg='30000' inputMode='2' optionClass='system' optionId='腹部压痛' hint='腹部压痛'>
										<Hint cfg='1000'>
											<Font color='808080' />腹部压痛</Hint>
									</Element>
									<Font color='0' />，<Element name='肌紧张' cfg='30000' inputMode='2' optionClass='system' optionId='肌紧张' hint='肌紧张'>
										<Hint cfg='1000'>
											<Font color='808080' />肌紧张</Hint>
									</Element>
									<Font color='0' />，Murhpy征<Element name='Murhpy征' cfg='30000' inputMode='2' optionClass='system' optionId='Murhpy征' hint='Murhpy征'>
										<Hint cfg='1000'>
											<Font color='808080' />Murhpy征</Hint>
									</Element>
									<Font color='0' />，<Element name='肠鸣音情况' cfg='30000' inputMode='2' optionClass='system' optionId='肠鸣音情况' hint='肠鸣音情况'>
										<Hint cfg='1000'>
											<Font color='808080' />肠鸣音情况</Hint>
									</Element>
									<Font color='0' />，<Element name='颈强' cfg='30000' inputMode='2' optionClass='system' optionId='颈强' hint='颈强'>
										<Hint cfg='1000'>
											<Font color='808080' />颈强</Hint>
									</Element>
									<Font color='0' />。<Element cfg='30000' hint='四肢肌力'>
										<Hint cfg='1000'>
											<Font color='808080' />四肢肌力</Hint>
									</Element>
									<Font color='0' />
									<Element name='等级' cfg='30000' inputMode='2' optionClass='system' optionId='等级' hint='等级'>
										<Hint cfg='1000'>
											<Font color='808080' />等级</Hint>
									</Element>
									<Font color='0' />级，病理征<Element name='阴阳性' cfg='30000' inputMode='2' optionClass='system' optionId='阴阳性' hint='阴阳性'>
										<Hint cfg='1000'>
											<Font color='808080' />阴阳性</Hint>
									</Element>
									<Font color='0' />，<Element cfg='30000' hint='其他'>
										<Hint cfg='1000'>
											<Font color='808080' />其他</Hint>
									</Element>
									<Font color='0' />。</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph xCfg='4' spaceAfter='0.080'>
					<Table id='table5' rows='1' cols='3' padding='0.012'>
						<Row height='0.800'>
							<Cell xCfg='1' width='7.308' borderWidth='0.012' borderStyle='1' topPadding='0.080' bottomPadding='0.080' leftPadding='0.120' rightPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />辅助检查：</Paragraph>
								<Paragraph>检验</Paragraph>
								<Paragraph>
									<Font cfg='0' />
									<Element cfg='30000' hint='检验结果'>
										<Hint cfg='1000'>
											<Font color='808080' />检验结果</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>
									<Font cfg='1' />检查</Paragraph>
								<Paragraph>
									<Font cfg='0' />
									<Element cfg='30000' hint='检查结果'>
										<Hint cfg='1000'>
											<Font color='808080' />检查结果</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>
									<Element cfg='30000' hint='检查结果'>
										<Hint cfg='1000'>
											<Font color='808080' />检查结果</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>
									<Element cfg='30000' hint='检查结果'>
										<Hint cfg='1000'>
											<Font color='808080' />检查结果</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>
									<Element cfg='30000' hint='检查结果'>
										<Hint cfg='1000'>
											<Font color='808080' />检查结果</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='4.094' borderWidth='0.012' borderStyle='1' topPadding='0.080' bottomPadding='0.080' leftPadding='0.120' rightPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />初步诊断：</Paragraph>
								<Paragraph>
									<Font cfg='0' />1、<Element cfg='30000' hint='初步诊断1'>
										<Hint cfg='1000'>
											<Font color='808080' />初步诊断1</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>2、<Element cfg='30000' hint='初步诊断2'>
										<Hint cfg='1000'>
											<Font color='808080' />初步诊断2</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>3、<Element cfg='30000' hint='初步诊断3'>
										<Hint cfg='1000'>
											<Font color='808080' />初步诊断3</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>4、<Element cfg='30000' hint='初步诊断4'>
										<Hint cfg='1000'>
											<Font color='808080' />初步诊断4</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>5、<Element cfg='30000' hint='初步诊断5'>
										<Hint cfg='1000'>
											<Font color='808080' />初步诊断5</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='4.432' borderWidth='0.012' borderStyle='1' topPadding='0.080' bottomPadding='0.080' leftPadding='0.120' rightPadding='0.120'>
								<Paragraph>
									<Font size='0.370' cfg='1' />确认诊断：</Paragraph>
								<Paragraph>
									<Font cfg='0' />1、<Element name='EMR_PatDiagnose.OtherDiagnoseName1' cfg='30000' hint='诊断名称1'>
										<Hint cfg='1000'>
											<Font color='808080' />诊断名称1</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>2、<Element name='EMR_PatDiagnose.OtherDiagnoseName2' cfg='30000' hint='诊断名称2'>
										<Hint cfg='1000'>
											<Font color='808080' />诊断名称2</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>3、<Element name='EMR_PatDiagnose.OtherDiagnoseName3' cfg='30000' hint='诊断名称3'>
										<Hint cfg='1000'>
											<Font color='808080' />诊断名称3</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>4、<Element name='EMR_PatDiagnose.OtherDiagnoseName4' cfg='30000' hint='诊断名称4'>
										<Hint cfg='1000'>
											<Font color='808080' />诊断名称4</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph>5、<Element name='EMR_PatDiagnose.OtherDiagnoseName5' cfg='30000' hint='诊断名称5'>
										<Hint cfg='1000'>
											<Font color='808080' />诊断名称5</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph xCfg='4'>医师签名：<Font cfg='404' />
					<Element name='DIC_EMC_EMR.EME_CreateUser' cfg='30000' hint='医师姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />医师姓名</Hint>
					</Element>
					<Font color='0' />
					<Space count='12' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />
					<Space count='7' />
					<Element name='DIC_EMC_EMR.EME_CreateTime' cfg='30000' inputMode='3' hint='创建时间'>
						<Hint cfg='1000'>
							<Font color='808080' />创建时间</Hint>
					</Element>
					<Font color='0' />
					<Space count='2' />
				</Paragraph>
				<Paragraph xCfg='0'>
					<Separator height='0.135' lineHeight='0.026' lCfg='5' />抢救情况：<Element name='抢救情况' cfg='30000' inputMode='2' optionClass='system' optionId='抢救情况' hint='抢救情况'>
						<Hint cfg='1000'>
							<Font color='808080' />抢救情况</Hint>
					</Element>
					<Font color='0' />
					<Space count='2' />转归：<Element name='转归情况' cfg='30000' inputMode='2' optionClass='system' optionId='转归情况' hint='转归情况'>
						<Hint cfg='1000'>
							<Font color='808080' />转归情况</Hint>
					</Element>
					<Font color='0' />
					<Space />
					<Element cfg='30000' hint='  '>住院科室：<Element cfg='30000' hint='科室名称'>
							<Hint cfg='1000'>
								<Font color='808080' />科室名称</Hint>
						</Element>
						<Font color='0' />
						<Space count='2' />押金：<Element cfg='30000' hint='XXXX'>
							<Hint cfg='1000'>
								<Font color='808080' />XXXX</Hint>
						</Element>
						<Font color='0' />元</Element>
				</Paragraph>
				<Paragraph>离院医嘱：<Element name='离院医嘱' cfg='30000' inputMode='2' optionClass='system' optionId='离院医嘱' hint='离院医嘱'>
						<Hint cfg='1000'>
							<Font color='808080' />离院医嘱</Hint>
					</Element>
					<SelectionBegin side='1' />
					<SelectionEnd side='1' />
					<Font color='0' />
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes>
			<Options class='system'>
				<Option id='过敏药物'>
					<Item id='青霉素'>青霉素</Item>
					<Item id='头孢类'>头孢类</Item>
					<Item id='碘胺类'>碘胺类</Item>
					<Item id='碘剂'>碘剂</Item>
					<Item id='水杨酸类'>水杨酸类</Item>
				</Option>
				<Option id='过敏食物'>
					<Item id='鱼、虾类'>鱼、虾类</Item>
					<Item id='豆制品'>豆制品</Item>
					<Item id='水果'>水果</Item>
				</Option>
				<Option id='神志情况'>
					<Item id='神志清楚'>神志清楚</Item>
					<Item id='嗜睡'>嗜睡</Item>
					<Item id='昏睡'>昏睡</Item>
					<Item id='浅昏迷'>浅昏迷</Item>
					<Item id='深昏迷'>深昏迷</Item>
				</Option>
				<Option id='言语'>
					<Item id='言语流利'>言语流利</Item>
					<Item id='言语不利'>言语不利</Item>
					<Item id='无法言语'>无法言语</Item>
				</Option>
				<Option id='体位'>
					<Item id='自主'>自主</Item>
					<Item id='平卧'>平卧</Item>
					<Item id='半卧'>半卧</Item>
					<Item id='俯卧'>俯卧</Item>
					<Item id='被动'>被动</Item>
					<Item id='强迫停立'>强迫停立</Item>
					<Item id='强迫卧'>强迫卧</Item>
					<Item id='强迫蹲'>强迫蹲</Item>
				</Option>
				<Option id='皮肤情况'>
					<Item id='皮肤无黄染'>皮肤无黄染</Item>
					<Item id='皮肤黄染'>皮肤黄染</Item>
					<Item id='皮肤无出血点'>皮肤无出血点</Item>
					<Item id='皮肤紫癜'>皮肤紫癜</Item>
					<Item id='皮肤散在出血点'>皮肤散在出血点</Item>
					<Item id='皮下出血'>皮下出血</Item>
				</Option>
				<Option id='配合检查'>
					<Item id='合作'>合作</Item>
					<Item id='不合作'>不合作</Item>
				</Option>
				<Option id='巩膜情况'>
					<Item id='巩膜无黄染'>巩膜无黄染</Item>
					<Item id='巩膜黄染'>巩膜黄染</Item>
				</Option>
				<Option id='结膜情况'>
					<Item id='无苍白'>无苍白</Item>
					<Item id='苍白'>苍白</Item>
					<Item id='充血'>充血</Item>
				</Option>
				<Option id='口唇情况'>
					<Item id='红润'>红润</Item>
					<Item id='发绀'>发绀</Item>
					<Item id='苍白'>苍白</Item>
				</Option>
				<Option id='颈静脉'>
					<Item id='无怒张'>无怒张</Item>
					<Item id='充盈'>充盈</Item>
					<Item id='怒张'>怒张</Item>
				</Option>
				<Option id='胸廓间接压痛'>
					<Item id='胸廓间接压痛阴性'>胸廓间接压痛阴性</Item>
					<Item id='胸廓间接压痛阳性'>胸廓间接压痛阳性</Item>
				</Option>
				<Option id='心肺整体情况'>
					<Item id='，心肺未见异常。'>，心肺未见异常。</Item>
					<Item id='，'>，</Item>
				</Option>
				<Option id='呼吸情况'>
					<Item id='呼吸平稳'>呼吸平稳</Item>
					<Item id='呼吸急促'>呼吸急促</Item>
					<Item id='呼吸深大'>呼吸深大</Item>
					<Item id='呼吸减慢'>呼吸减慢</Item>
					<Item id='无自主呼吸'>无自主呼吸</Item>
				</Option>
				<Option id='肺呼吸音'>
					<Item id='左肺呼吸音清'>左肺呼吸音清</Item>
					<Item id='左肺呼吸音粗'>左肺呼吸音粗</Item>
					<Item id='左肺呼吸音弱'>左肺呼吸音弱</Item>
					<Item id='右肺呼吸音清'>右肺呼吸音清</Item>
					<Item id='右肺呼吸音粗'>右肺呼吸音粗</Item>
					<Item id='右肺呼吸音弱'>右肺呼吸音弱</Item>
					<Item id='双肺呼吸音清'>双肺呼吸音清</Item>
					<Item id='双肺呼吸音粗'>双肺呼吸音粗</Item>
					<Item id='双肺呼吸音弱'>双肺呼吸音弱</Item>
				</Option>
				<Option id='干湿啰音'>
					<Item id='未闻及干湿啰音'>未闻及干湿啰音</Item>
					<Item id='湿啰音'>湿啰音</Item>
					<Item id='干啰音'>干啰音</Item>
				</Option>
				<Option id='心律情况'>
					<Item id='规整'>规整</Item>
					<Item id='不齐'>不齐</Item>
					<Item id='期前收缩'>期前收缩</Item>
				</Option>
				<Option id='全腹情况'>
					<Item id='平软'>平软</Item>
					<Item id='板状'>板状</Item>
					<Item id='膨隆'>膨隆</Item>
				</Option>
				<Option id='腹部压痛'>
					<Item id='无压痛'>无压痛</Item>
					<Item id='有压痛'>有压痛</Item>
				</Option>
				<Option id='肌紧张'>
					<Item id='无肌紧张'>无肌紧张</Item>
					<Item id='有肌紧张'>有肌紧张</Item>
				</Option>
				<Option id='Murhpy征'>
					<Item id='阴性'>阴性</Item>
					<Item id='阳性'>阳性</Item>
				</Option>
				<Option id='肠鸣音情况'>
					<Item id='肠鸣音正常'>肠鸣音正常</Item>
					<Item id='肠鸣音亢进'>肠鸣音亢进</Item>
					<Item id='肠鸣音弱'>肠鸣音弱</Item>
					<Item id='肠鸣音未闻及'>肠鸣音未闻及</Item>
				</Option>
				<Option id='颈强'>
					<Item id='颈强阴性'>颈强阴性</Item>
					<Item id='颈强阳性'>颈强阳性</Item>
				</Option>
				<Option id='等级'>
					<Item id='Ⅴ'>Ⅴ</Item>
					<Item id='Ⅳ'>Ⅳ</Item>
					<Item id='Ⅲ'>Ⅲ</Item>
					<Item id='Ⅱ'>Ⅱ</Item>
					<Item id='Ⅰ'>Ⅰ</Item>
				</Option>
				<Option id='阴阳性'>
					<Item id='阴性'>阴性</Item>
					<Item id='阳性'>阳性</Item>
				</Option>
				<Option id='抢救情况'>
					<Item id='无'>无</Item>
					<Item id='成功'>成功</Item>
					<Item id='急诊室内死亡'>急诊室内死亡</Item>
				</Option>
				<Option id='转归情况'>
					<Item id='回家'>回家</Item>
					<Item id='住院'>住院</Item>
					<Item id='转它院'>转它院</Item>
					<Item id='死亡'>死亡</Item>
				</Option>
				<Option id='离院医嘱'>
					<Item id='定期复查'>定期复查</Item>
					<Item id='病情变化随时就诊'>病情变化随时就诊</Item>
					<Item id='3天换药，7—10天拆线' weight='3.000'>3天换药，7—10天拆线</Item>
				</Option>
			</Options>
		</OptionRes>
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
