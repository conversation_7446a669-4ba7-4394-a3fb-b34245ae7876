export declare function Sleep(seconds: number): Promise<unknown>;
export declare function SetFontConfigCfgValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function InsertSeparatorCfgValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function ColorValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function NotEmptyStrValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function StrValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function NumValidator(limitStr: any): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function IntValidator(): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function Format(fmt: RegExp): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function Fields(fields: string[]): (target: any, propertyKey: string, parameterIndex: number) => void;
export declare function NotNull(target: any, propertyKey: string, parameterIndex: number): void;
export declare function Validator(target: any, propertyKey: string, descriptor: PropertyDescriptor): void;
export declare function IsHavingAdvancedPermissions(): boolean;
export declare function IsElectron(): boolean;
export declare function GetSysPlatform(): "Windows" | "Linux" | "Mac" | "Unknown";
export declare function IsASCII(word: string): boolean;
export declare function getOsInfo(): string;
export declare function getBrowerInfo(): string;
export declare function Date2TimeStr(date: Date, fmt?: string): string;
export declare function TimeStr2Date(dateTimeStr: string): Date;
export declare function TimeStr2TimeStamp(dateTimeStr: string): number;
export declare function TimeStamp2TimeStr(timeStamp: number): string;
export declare function IsBrowserSupportLocalFonts(): boolean;
export declare function GetLocalFontsPermissionState(): Promise<PermissionState>;
export declare function Pound2CM(pd: number): number;
export declare function PartUpdate(srcObj: any, destObj: any, that: any): void;
export declare function UpdateConfigData(that: any, srcObj: any): void;
export declare function CalcDocName(docName: string): string;
export declare function Color2RGBA(color: string): {
    a: number;
    r: number;
    g: number;
    b: number;
};
export declare function RGBA2Color(rgba: any): string;
export declare function RGBA2ColorNum(rgba: any): number;
export declare function ColorNum2RGBAStr(colorNum: number): string;
export declare function RGBA2RGBAStr(rgba: any): string;
export declare function RGBAStr2RGBA(rgbaStr: string): {};
export declare function RGBAStr2Color(rgbaStr: string): string;
export declare function Color2RGBAStr(color: string): string | undefined;
export declare function ToPlainTextByCDATA(text: string): string;
export declare function ToPlainTextByEscape(text: string): string;
export declare function ShowSaveFileDialog(fileName: string, dataBuffer: ArrayBuffer): void;
export declare function GetPrintIframe(): HTMLIFrameElement;
export declare function GetRandStr(): string;
export declare function PopFileSelector(fileLimit: string): Promise<unknown>;
export declare function GetFirstPinYinLetter(unicodeStr: string): string;
//# sourceMappingURL=ThinkEditor.Utils.d.ts.map