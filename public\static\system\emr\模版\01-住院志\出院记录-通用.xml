<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='12' lineSpaceValue='1.500'>
					<Font size='0.767'  cfg='1' />出院记录</Paragraph>
				<Paragraph>
					<Font size='0.397' cfg='0' />
					<Space count='20' />
					<Font size='0.317' cfg='1' /> Discharge Note<Font size='0.388' cfg='0' /> <Space count='10' />
					<Separator height='0.123' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph xCfg='10'>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住  院  科  室  名  称'>
						<Hint cfg='1000'>
							<Font color='808080' />住<Space count='2' />院<Space count='2' />科<Space count='2' />室<Space count='2' />名<Space count='2' />称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />
					<Font size='0.397' />H01-06-YW-014                                                   第<PageNum width='0.580' height='0.469' lCfg='2'>
						<Unit width='0.580' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />
					<Font size='0.397' />页  共<PageNum xCfg='1' width='0.851' height='0.469' lCfg='2'>
						<Unit width='0.851' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页                                                        2014-12-A1</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='9' cols='3' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='7.080' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院时间：<Font cfg='0' />
									<Element name='PinDateSysTime' cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='6.741' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院日期：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' hint='yyyy年MM月dd日'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='3.612' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />住院天数：<Font cfg='0' />
									<Element cfg='30000' inputMode='1' hint='几'>
										<Hint cfg='1000'>
											<Font color='808080' />几</Hint>
									</Element>
									<Font color='0' cfg='1' />天</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院诊断：<Font cfg='0' />
									<Element cfg='30000' hint='双击选择入院诊断'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择入院诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院情况：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括入院原因、主诉、病情、体查发现，主要辅助检查结果等）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括入院原因、主诉、病情、体查发现，主要辅助检查结果等）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />诊疗经过：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括住院期间诊疗过程效果，重要的药品治疗（含药物名称、剂量、使用频次、途径）等，已施行的诊断性和治疗性的操作，凡接受手术治疗患者，应详细记录所做手术方式、术后主要治疗及方式、病理切片结果等。）'>所得到的封锁地带</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院情况：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括疾病恢复程度，包括症状、体征、后遗症、辅助检查结果等。）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括疾病恢复程度，包括症状、体征、后遗症、辅助检查结果等。）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
								<Paragraph leftIndent='0.000' rightIndent='0.000'>
									<Font cfg='1' />是否有植入物<Font cfg='0' />：<Element cfg='30000' hint='选择是否有植入物'>
										<Hint cfg='1000'>
											<Font color='808080' />选择是否有植入物</Hint>
									</Element>
									<Font size='0.397' color='0' cfg='404' />
									<Element cfg='30000' hint='（如有植入物，向患者宣教与植入物相关的健康教育信息。）'>
										<Hint cfg='1000'>
											<Font color='808080' />（如有植入物，向患者宣教与植入物相关的健康教育信息。）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院诊断：<Font cfg='0' />
									<Element cfg='30000' hint='双击选择出院诊断'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择出院诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院医嘱：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括出院带药的名称、数量、剂量、用法；处置。）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括出院带药的名称、数量、剂量、用法；处置。）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />随访指导及健康教育：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括建议复诊的时间和项目及需紧急处理的病症；活动；饮食营养；康复；健康教育。）'>
										<LF />
										<Space count='9' />1.出院后注意观察<Element cfg='30000' hint='(与本次住院疾病直接相关的症状与体征)'>
											<Hint cfg='1000'>
												<Font color='808080' />(与本次住院疾病直接相关的症状与体征)</Hint>
										</Element>
										<Font color='0' />情况，防止<Element cfg='30000' hint='(疾病相关的诱发因素，或其他，如跌倒等)'>
											<Hint cfg='1000'>
												<Font color='808080' />(疾病相关的诱发因素，或其他，如跌倒等)</Hint>
										</Element>
										<Font color='0' />。如有<Element cfg='30000' hint='(需要进行紧急医疗处理的症状与体征)'>
											<Hint cfg='1000'>
												<Font color='808080' />(需要进行紧急医疗处理的症状与体征)</Hint>
										</Element>
										<Font color='0' />情况，立即复诊。<LF />
										<Space count='9' />2.请于出院后第<Element cfg='30000' hint='**'>
											<Hint cfg='1000'>
												<Font color='808080' />**</Hint>
										</Element>
										<Font color='0' />
										<Element cfg='30000' hint='  '>周</Element>到<Element cfg='30000' hint='（科室名称）'>
											<Hint cfg='1000'>
												<Font color='808080' />（科室名称）</Hint>
										</Element>
										<Font color='0' />门诊复查<Element cfg='30000' hint='（检查、检验项目）'>
											<Hint cfg='1000'>
												<Font color='808080' />（检查、检验项目）</Hint>
										</Element>
										<Font color='0' />。<Space />
										<LF />
										<Space count='9' />2.请于出院后第<Element cfg='30000' hint='**'>
											<Hint cfg='1000'>
												<Font color='808080' />**</Hint>
										</Element>
										<Font color='0' />
										<Element cfg='30000' hint='  '>周</Element>返院进行<Element cfg='30000' hint='（住院治疗项目，如内置物取出或化疗等）'>
											<Hint cfg='1000'>
												<Font color='808080' />（住院治疗项目，如内置物取出或化疗等）</Hint>
										</Element>
										<Font color='0' />。<LF />
										<Space count='9' />2.请于出院后第<Element cfg='30000' hint='**'>
											<Hint cfg='1000'>
												<Font color='808080' />**</Hint>
										</Element>
										<Font color='0' />
										<Element cfg='30000' hint='  '>周</Element>在当地医院或者社区医疗机构复查<Element cfg='30000' hint='（检查、检验项目）'>
											<Hint cfg='1000'>
												<Font color='808080' />（检查、检验项目）</Hint>
										</Element>
										<Font color='0' />。<LF />
										<Space count='9' />3.<Space count='7' />
										<LF />
										<Space count='9' />4.<Space count='7' />
										<LF />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.433' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />交通需求：<Font cfg='0' />
									<Element cfg='30000' hint='（采用何种交通工具离院）'>
										<Hint cfg='1000'>
											<Font color='808080' />（采用何种交通工具离院）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph xCfg='14' leftIndent='0.000' rightIndent='0.000'>医师签名/工号：<Font cfg='404' />
					<Space count='18' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />时<Space count='7' />间：<Font cfg='404' />
					<Space count='20' />
				</Paragraph>
				<Paragraph />
				<Paragraph xCfg='12'>
					<Font size='0.423' cfg='1' />门诊预约和就诊挂号说明</Paragraph>
				<Paragraph xCfg='10'>
					<Font size='0.370' />一、适合您的预约方式如下：</Paragraph>
				<Paragraph>
					<Font cfg='0' />1.电话预约：联系人工预约专线0000-8277000,(周一至周五:上午8:00—11:30，下午13:30—17:00，周六8:00-11:30，如遇节假日休息)<Space />。</Paragraph>
				<Paragraph>2.网络预约（登录湖南旺旺医院官方网站http://www.thinkedito<SelectionBegin side='2' />
					<SelectionEnd side='2' />r.com首页的【网上预约】按钮进入）。</Paragraph>
				<Paragraph>3.微信平台预约（请您在1楼门诊量身高、血压窗口先进行微信注册）。</Paragraph>
				<Paragraph>
					<Font cfg='1' />二、门诊预约挂号说明：</Paragraph>
				<Paragraph>
					<Font cfg='0' />1.适用于复诊病患。<Space />2.预约看诊当天须携带有效身份证明,在医疗楼一楼门诊大厅5号挂号窗口缴费。<Space />3.初诊病人请先至分诊台填写个人信息登记表,缴费时一并交付给收费柜台收费员。</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
