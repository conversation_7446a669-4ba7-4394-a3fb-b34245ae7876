// let Base64 = require('js-base64').Base64;
import {Base64} from 'js-base64';

import { E_LINE_SPACE_RULE, optionsManager, E_SYSTEM_AUTH_MODE, E_PASTE_TYPE, SeparatorCfg, BarCodeCfg, QCCfg, 
  E_SET_MODE, E_FERRULE_MODE, ElementCfg, E_DOC_TYPE, E_DISPLAY_MODE, E_DOCS_ORGANIZE_MODE,
   E_PAGES_LAYOUT_MODE, E_VIEW_MODE, E_FORMULA_STYLE, E_LAYOUT_VERTICAL_ALIGN, 
   E_BARCODE_TYPE, E_CHECK_FIGURE_STYLE, E_DOC_FORMAT, 
     E_IMAGE_TYPE } from '@/components/editor/ThinkEditor.Defined';
    // E_LIST_MODEL,E_ALIGN_HORIZONTAL_MODE,
    //E_IDENTITY_OBJECT_TYPE, SearchReplaceCfg, AttachCfg,  E_TIME_TYPE,

import { GetRandStr, ShowSaveFileDialog, PopFileSelector } from '@/components/editor/ThinkEditor.Utils.js';
import { ThinkEditor } from '@/components/editor/ThinkEditor';

export class ThinkEditorDemo {
  constructor(thinkEditor) {
    this.thinkEditor = thinkEditor;
  }
  bind(thinkEditor) {
    this.thinkEditor = thinkEditor;
  }
  // 全屏事件
  TTTT() {
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit);
    this.thinkEditor.SetElementContent('sign', '');
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Browse);
    var xml_str = this.thinkEditor.GetDoc(E_DOC_FORMAT.XML, E_DOC_TYPE.Entity);
    console.log(xml_str);
  }

  /*
     SetWaterMarkConfig
     SetUserDefinedMenu
     SetFontColor
     SetFontBackColor
    SetFontType
    SetFontSize
UnionTest
UnionTimeTest
SetElementContent
SetParagraphContent
Run
Quit
InsertSeparator
DeleteElement,
DisplayFontSetWindow,
DisplayParagraphSetWindow,
禁用右键菜单
SetDocConfig
InsertPageNum
GetDoc
PrintDoc
SetElementSelectDateTime
SetElementSource
InputData
ToPlainTextByEscape
testDump
ReleaseEditor
CloseAllDoc
CloseDoc
AddDocAttribute
SetBackgroundColor
SetPageConfig
SetDisplayScale
InsertParagraph
GetSelectRangeContent
SetElementConfig
DeleteParagraph
ReplaceTemp
GetElementsContent
GetElementProperties
InitAsync
GetPageProperties
     */
  async Test1() {
    var testCode = 'SetTableData';

    var json_cfg = {};
    switch (testCode) {
      case 'SetTableData': {
        let rowHeight = 1.0;

        let tableXml = '<Table>';

        tableXml += `<Row height='${rowHeight}'>`;
        tableXml += `<Cell ><Paragraph>a</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>b</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>c</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>d</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>e</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>f</Paragraph></Cell>`;
        tableXml += `</Row>`;

        tableXml += `<Row height='${rowHeight}'>`;
        tableXml += `<Cell ><Paragraph>a</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>b</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>c</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>d</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>e</Paragraph></Cell>`;
        tableXml += `<Cell ><Paragraph>f</Paragraph></Cell>`;
        tableXml += `</Row>`;

        tableXml += '</Table>';
        this.thinkEditor.SetTableData('generalCheck', tableXml, {});
        //var result = this.thinkEditor.SetTableData();
        break;
      }
      case 'GetPageProperties': {
        var result = this.thinkEditor.GetPageProperties();
        break;
      }
      case 'InitAsync': {
        await this.thinkEditor.InitAsync();

        var newDocName = '123';
        var url = '/system/emr/thinkeditor.xml'; //"/system/test/pacsdemo.jpg";
        let response = await fetch(url, {
          credentials: 'same-origin'
        });
        let xmlStr = await response.arrayBuffer();
        this.thinkEditor.ParseDoc(newDocName, xmlStr, E_DOCS_ORGANIZE_MODE.Single);
        this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, {});

        break;
      }
      case 'SetParagraphLineSpacingValue': {
        var result = this.thinkEditor.SetParagraphLineSpacingValue(undefined);
        break;
      }

      case 'GetElementProperties': {
        var result = this.thinkEditor.GetElementProperties({ mode: 1 });
        break;
      }
      case 'GetElementsContent': {
        var result = this.thinkEditor.GetElementsContent(['简要诊断', 'city']);
        break;
      }
      case 'ReplaceTemp': {
        var doc_name = this.thinkEditor.GetVisibleDocName();

        await this.thinkEditor.CloseDoc(doc_name);
        var newDocName = '123';
        var url = '/system/emr/thinkeditor.xml'; //"/system/test/pacsdemo.jpg";
        let response = await fetch(url, {
          credentials: 'same-origin'
        });
        let xmlStr = await response.arrayBuffer();
        this.thinkEditor.CreateDoc(newDocName);
        this.thinkEditor.SetDocType(E_DOC_TYPE.Entity);
        this.thinkEditor.ParseDoc(newDocName, xmlStr, E_DOCS_ORGANIZE_MODE.Single);
        this.thinkEditor.SetEditMode(false, false, false, false, false);
        this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, {});

        this.thinkEditor.showName = this.thinkEditor.GetVisibleDocName();
        break;
      }
      case 'DeleteParagraph': {
        this.thinkEditor.DeleteParagraph({
          ids: ['主诉', '既往史']
        });

        break;
      }
      case 'SetReviseConfig': {
        this.thinkEditor.SetReviseConfig({
          mode: 1,
          cleanAddRevise: true,
          cleanDeleteRevise: true
        });

        break;
      }
      case 'GetOutline': {
        var outline = this.thinkEditor.GetOutline({
          mode: 2
        });
        console.log(JSON.stringify(outline));
        break;
      }
      case 'SetElementConfig': {
        this.thinkEditor.SetElementConfig('qwe', {
          hint: '请输入时间',
          inputHandle: { inputMode: 3 }
        });
        break;
      }
      case 'InputData': {
        this.thinkEditor.InputData('<Fragment>' + '<LF/><Element id="住院证" name="住院证" cfg="30000" hint="请输入">建议住院治疗。</Element>' + '</Fragment>');
        this.thinkEditor.SetElementConfig('住院证', {
          hide: 0,
          lockedContent: 0,
          forbidDelete: 0
        });
        //this.thinkEditor.DeleteElement('住院证', {});
        break;
      }
      case 'SetOptions': {
        //-----------------------------------

        const optionsData = [
          {
            optionClass: '专科检查-四肢',
            optionId: '足背动脉搏动',
            items: [
              {
                id: '足背动脉搏动可触及',
                group: '1',
                weight: 1,
                content: '足背动脉搏动可触及'
              },
              {
                id: '足背动脉搏动不可触及',
                group: '1',
                weight: 1,
                content: '足背动脉搏动不可触及'
              },
              {
                id: '足背动脉搏动正常',
                group: '1',
                weight: 1,
                content: '足背动脉搏动正常'
              }
            ]
          }
        ];

        const optionsData1 = [
          {
            optionClass: '专科检查-四肢',
            optionId: '足背动脉搏动',
            items: [
              {
                id: '1111',
                group: '1',
                weight: 1,
                content: '1111'
              },
              {
                id: '222222',
                group: '1',
                weight: 1,
                content: '222222'
              },
              {
                id: '3333',
                group: '1',
                weight: 1,
                content: '3333'
              }
            ]
          }
        ];

        optionsManager.AddOptions(optionsData);
        console.log(optionsManager.GetOption('专科检查-四肢', '足背动脉搏动'));
        optionsManager.AddOptions(optionsData1);
        console.log(optionsManager.GetOption('专科检查-四肢', '足背动脉搏动'));
        //------------------------------------------
        break;
      }
      case 'GetDocProperties': {
        var docProperties = this.thinkEditor.GetDocProperties();
        console.warn(docProperties);
        break;
      }
      case 'InsertTable': {
        var test = this.thinkEditor.InsertTable({
          id: 'id',
          name: 'name',
          rowsCount: 1,
          colsCount: 2,
          lockedFormat: false,
          alignHorizontalMode: '0',
          leftIndent: '0.00',
          topPadding: '0.1',
          bottomPadding: '0.1',
          leftPadding: '0.1',
          rightPadding: '0.1',
          border: {
            apply: 'table',
            topBorderStyle: 0,
            bottomBorderStyle: 0,
            rightBorderStyle: 0,
            innerHorizontalBorderStyle: 0,
            innerVerticalBorderStyle: 0,
            leftBorderStyle: 0,
            rightBorderStyle: 0,
            borderWidth: '0.02'
          }
        });
        break;
      }
      case 'SetTableConfig': {
        var test = this.thinkEditor.SetTableConfig('', {
          lockedFormat: false,
          alignHorizontalMode: '0',
          leftIndent: '0.00',
          topPadding: '0.1',
          bottomPadding: '0.1',
          leftPadding: '0.1',
          rightPadding: '0.1',
          border: {
            borderSetMode: 0,
            borderStyle: 0,
            topBorderColor: 'ffd9d9d9',
            bottomBorderColor: 'ffd9d9d9',
            leftBorderColor: 'ffd9d9d9',
            rightBorderColor: 'ffd9d9d9',
            borderWidth: '0.02'
          }
        });
        break;
      }
      case 'GetSelectRangeContent': {
        var test = this.thinkEditor.GetSelectRangeContent(E_DOC_FORMAT.TEXT);
        break;
      }
      case 'InsertParagraph': {
        this.thinkEditor.InsertParagraph({});
        break;
      }
      case 'InsertElement': {
        var cfg = {
          object: 'element',
          id: '',
          name: '双肺呼吸音',
          empty: true,
          text: '',
          content: '',
          fragment: "<Fragment><Element cfg='30000' hint='请输入'><Hint cfg='1000'><Font color='808080'/>请输入</Hint></Element></Fragment>",
          beginBorder: '',
          endBorder: '',
          beforeTag: '',
          afterTag: '',
          hint: '双肺呼吸音',
          tips: '',
          width: 0,
          alignHorizontalMode: 0,
          hideKeyWord: 0,
          forbidDelete: false,
          lockedContent: false,
          lockedFormat: false,
          forbidSpace: false,
          enableBackgroundEffects: true,
          enableFocusedEffects: true,
          widthUnderline: false,
          allowJump: 0,
          fitContent: 0,
          displayBorder: 0,
          splitParagraphMode: 0,
          source: {
            sourceClass: 'aaa',
            sourceId: 'bbb',
            dynamicLoad: 1
          },
          algorithm: {},
          inputHandle: {
            inputMode: 2,
            optionClass: '专科检查-胸',
            optionId: '双肺呼吸音',
            items: [
              {
                id: '双肺呼吸音清',
                group: '1',
                weight: 1,
                content: '双肺呼吸音清'
              },
              {
                id: '双肺呼吸音粗',
                group: '1',
                weight: 1,
                content: '双肺呼吸音粗'
              }
            ],
            linkStr: '、',
            optionShowMode: 0
          },
          qualityControl: {
            qcClass: 0,
            level: 0,
            forbidEmpty: 0,
            notice: '',
            exclude: '',
            useByteLen: 0,
            useMinLimit: 0,
            min: 0,
            useMaxLimit: 0,
            max: 0
          },
          expressions: [
            {
              event: 0,
              mode: 0,
              action: '',
              popVisible: false
            }
          ]
        };
        //this.thinkEditor.InsertElement({ font: { fontSizeItem: '小五' } });

        this.thinkEditor.InsertElement(cfg);

        break;
      }
      case 'SetDisplayScale': {
        this.thinkEditor.SetDisplayScale(E_PAGES_LAYOUT_MODE.ViewWidth, 1);
        break;
      }
      case 'SetPageConfig': {
        this.thinkEditor.SetPageConfig({
          width: 21,
          height: 29.7,
          id: '',
          name: '',
          notSave: false,
          alignVerticalLayout: 'Bottom',
          bindLine: {
            margin: 0,
            position: 16,
            showLine: false
          },
          bottomPadding: 2,
          leftPadding: 3.14,
          rightPadding: 3.14,
          topPadding: 2.54,
          header: {
            topMargin: 2,
            hide: 0,
            showLine: false,
            locked: false,
            dynamicLocked: false
          },
          footer: {
            bottomMargin: 0.3,
            hide: 0,
            showLine: false,
            locked: false,
            dynamicLocked: false
          },
          evenPageDiff: false,
          fristPageDiff: false
        });
        break;
      }
      case 'SetFontBackColor': {
        this.thinkEditor.SetFontBackColor('000000');
        //this.thinkEditor.SetFontColor('00000000');
        break;
      }
      case 'SetFontColor': {
        this.thinkEditor.SetFontColor('000000');
        //this.thinkEditor.SetFontColor('00000000');
        break;
      }
      case 'SetBackgroundColor': {
        //RGBA2Color({ a: 255, r: 66, g: 133, b: 244 })
        this.thinkEditor.SetBackgroundColor('ffF5F5F5');
        break;
      }
      case 'SetDefaultFont': {
        this.thinkEditor.SetDefaultFont('宋体', '小四');
        break;
      }
      case 'AddDocAttribute': {
        this.thinkEditor.AddDocAttribute('key1', 'value1');
        this.thinkEditor.AddDocAttribute('key2', 'value2');
        var docProperties = this.thinkEditor.GetDocProperties();
        console.log(docProperties);
        break;
      }
      case 'ReleaseEditor': {
        this.thinkEditor.UnInit();
        break;
      }
      case 'testDump': {
        this.thinkEditor.SetEditorConfig({ testDump: 1 });
        break;
      }

      case 'ToPlainTextByEscape': {
        var str1 = '(快&检) <1"0.00 mg/L11111&2\'222<222<23>3333>33"33>3\'';
        //'(快&amp;检) &lt;1&quot;0.00 mg/L11111&amp;2&apos;222&lt;222&lt;23&gt;3333&gt;33&quot;33&gt;3&apos;'
        var plainText1 = this.thinkEditor.ToPlainTextByEscape(str1);
        plainText1 += '<LF/>';

        var str2 = '(放入元素中&) <1"0.00 mg/L有特殊字符<23>3333>33"33>3\'';
        var plainText2 = this.thinkEditor.ToPlainTextByEscape(str2);
        plainText2 = '<Element>' + plainText2 + '</Element>';
        //'<Fragment>(快&amp;检) &lt;1&quot;0.00 mg/L11111&amp;2&apos;222&lt;222&lt;23&gt;3333&gt;33&quot;33&gt;3&apos;</Fragment>'
        var fragmentStr = '<Fragment>' + plainText1 + plainText2 + '</Fragment>';

        this.thinkEditor.InputData(fragmentStr);
        break;
      }
      case 'ToPlainTextByCDATA': {
        var str = '(快&检) <1"0.00 mg/L111<![CDATA[11&2\'222<222<23>33]]>33>33"33>3\'';
        //`<![CDATA[(快&检) <1"0.00 mg/L11111&2'222<222<23>3333>33"33>3']]>`
        var plainText = this.thinkEditor.ToPlainTextByCDATA(str);
        //`<Fragment><![CDATA[(快&检) <1"0.00 mg/L11111&2'222<222<23>3333>33"33>3']]></Fragment>`
        var fragmentStr = '<Fragment>' + plainText + '</Fragment>';

        this.thinkEditor.InputData(fragmentStr);
        break;
      }
      case 'SetElementSource': {
        var ele_cfg = new ElementCfg();
        ele_cfg.dynamic_load_ = true;
        this.thinkEditor.SetElementSource('DFE02698-0904-464D-8ED0-E66E67BFC9FB', '', '', ele_cfg);
        break;
      }
      case 'SetElementSelectDateTime':
        {
          this.thinkEditor.SetElementSelectDateTime('', 4);
        }
        break;
      case 'PrintDoc':
        {
          console.log('--------PrintDoc-----------');
          this.thinkEditor.SetParagraphConfig('nodraw2', {
            hide: true
          });
          await this.thinkEditor.PrintDocAsync({
            sharpness: 2.0,
            rotate: 0,
            mode: 0,
            pagesRange: '',
            view: {
              mode: E_VIEW_MODE.Print,
              hideElementHint: true
            }
          });
          this.thinkEditor.SetParagraphConfig('nodraw2', {
            hide: false
          });
        }
        break;
      case 'GetDoc_JsonXmlTest':
        {
          var docXml = this.thinkEditor.GetDoc(E_DOC_FORMAT.XML, E_DOC_TYPE.Entity);
          console.log(docXml);
          var docJson = JSON.parse(docXml);
          console.log(docJson.xml);
          var docStr = Base64.decode(docJson.xml);
          console.log(docStr);
        }
        break;
      case 'InsertPageNum':
        {
          this.thinkEditor.InsertPageNum({ scope: E_PAGE_NUM_SCOPE.WholeDocnment, style: E_PAGE_NUM_STYLE.OrderListModel_0, format: E_PAGE_NUM_FORMAT.PageOrderPagesCountFormat });
        }
        break;
      case 'SetDocConfig':
        {
          //json_cfg.dynamicLocked = true;//_禁止正文编辑
          json_cfg.disableParagraphLockedTip = true;
          this.thinkEditor.SetDocConfig('', json_cfg);
        }
        break;
      case '禁用右键菜单':
        {
          json_cfg.clipBoardMenuDisplayMode = 0;
          json_cfg.attributeMenuDisplayMode = 1;
          json_cfg.operationMenuDisplayMode = 0;
          this.thinkEditor.SetEditorConfig(json_cfg);
        }
        break;
      case 'DisplayFontSetWindow':
        {
          this.thinkEditor.DisplayFontSetWindow(E_DISPLAY_MODE.Show);
        }
        break;
      case 'DisplayParagraphSetWindow':
        {
          this.thinkEditor.DisplayParagraphSetWindow(E_DISPLAY_MODE.Show);
        }
        break;
      case 'DeleteElement':
        {
          this.thinkEditor.DeleteElement('');
        }
        break;
      case 'InsertSeparator':
        {
          this.thinkEditor.InsertSeparator(0, 0.8, {
            lineWidth: 0.1,
            color: 'ffff0000'
          });
        }
        break;
      case 'SetParagraphContent':
        {
          this.thinkEditor.SetParagraphContent('主诉', '无UI模式');
        }
        break;
      case 'SetElementContent':
        {
          let fragement = `<Fragment>
          <Element id='menses' cfg='10000' hint='月经史'>
            <Formula xCfg='1' width='1.739' height='1.652' lCfg='2'>
              <Unit eCfg='5' width='0.580' height='0.826' padding='0.190'>
                <Paragraph lineSpaceValue='1.000'>
                  <Element cfg='30000' inputMode='3' hint='初潮年龄'>1</Element>
                  <Font color='808080'/>
                </Paragraph>
              </Unit>
              <Unit eCfg='5' width='0.580' height='0.826' padding='0.190'>
                <Paragraph lineSpaceValue='1.000'>
                  <Element cfg='30000' inputMode='1' hint='经期'>2</Element>
                  <Font color='808080'/>
                </Paragraph>
              </Unit>
              <Unit eCfg='5' width='0.580' height='0.826' padding='0.190'>
                <Paragraph lineSpaceValue='1.000'>
                  <Element cfg='30000' inputMode='1' hint='周期'>4</Element>
                  <Font color='808080'/>
                </Paragraph>
              </Unit>
              <Unit eCfg='5' width='0.580' height='0.826' padding='0.190'>
                <Paragraph lineSpaceValue='1.000'>
                  <Element cfg='30000' inputMode='3' hint='末次月经'>3</Element>
                  <Font color='808080'/>
                </Paragraph>
              </Unit>
            </Formula>
            <Font color='0'/>，月经规律周期<Element cfg='30000' inputMode='2' optionClass='入院记录' optionId='月经规律周期' items='正常' hint='请输入'>正常</Element>，月经量<Element cfg='30000' inputMode='2' optionClass='入院记录' optionId='月经量' items='正常' hint='请输入'>正常</Element>，色暗，<Element cfg='30000' inputMode='2' optionClass='入院记录' optionId='有无' items='无' hint='请输入'>无</Element>血块，<Element cfg='30000' inputMode='2' optionClass='入院记录' optionId='有无' items='无' hint='请输入'>无</Element>痛经。
            
            </Element>
        </Fragment>`;
          this.thinkEditor.SetElementContent('', fragement);
        }
        break;
      case 'Copy':
        {
          this.thinkEditor.Copy();
        }
        break;
      case 'loseContext':
        {
          //模拟丢失
          this.thinkEditor.gl.getExtension('WEBGL_lose_context').loseContext();
        }
        break;
      case 'SetViewMode_fontColor':
        {
          json_cfg.fontColor = 'ff0000ff';
          this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, json_cfg);
        }
        break;
      case 'UnionTest':
        {
          //this.thinkEditor.CloseAllDoc();
          //新建文档编辑器
          var docName = '合并';
          var thinkEditor = new ThinkEditor({
            lib: '/editor/', //库文件在服务器上路径
            fontPath: '/editor_fonts/', //字体文件在服务器上路径
            editorId: docName + '_' + GetRandStr(), //文档编辑器Id，用于区分不同的文档编辑器
            auth: { mode: E_SYSTEM_AUTH_MODE.Demonstration }
          });

          await thinkEditor.Init();

          var url = '/system/emr/1.xml'; //"/system/test/pacsdemo.jpg";
          let response = await fetch(url, {
            credentials: 'same-origin'
          });
          let xml1 = await response.arrayBuffer();

          json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionSection;
          json_cfg.recoverSelection = 2;
          json_cfg.setUnionSelection = 0;
          thinkEditor.CreateDoc('病程记录'); //@20220722 必需，才能设置编辑者信息
          thinkEditor.SelectDoc('病程记录');
          thinkEditor.SetEditorInfo('pc', 'zltest', 'zl', 1, '');
          thinkEditor.ParseDocs('病程记录', '首次病程记录-1-13333', xml1, json_cfg);

          url = '/system/emr/2.xml'; //"/system/test/pacsdemo.jpg";
          response = await fetch(url, {
            credentials: 'same-origin'
          });
          let xml2 = await response.arrayBuffer();
          json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionSection;
          json_cfg.recoverSelection = 2;
          json_cfg.setUnionSelection = 1;
          thinkEditor.ParseDocs('病程记录', '首次病程记录-1-147449', xml2, json_cfg);

          url = '/system/emr/3.xml'; //"/system/test/pacsdemo.jpg";
          response = await fetch(url, {
            credentials: 'same-origin'
          });
          let xml3 = await response.arrayBuffer();
          json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionSection;
          json_cfg.recoverSelection = 2;
          json_cfg.setUnionSelection = 0;
          thinkEditor.ParseDocs('病程记录', '首次病程记录-1-3', xml3, json_cfg);

          url = '/system/emr/4.xml'; //"/system/test/pacsdemo.jpg";
          response = await fetch(url, {
            credentials: 'same-origin'
          });
          let xml4 = await response.arrayBuffer();
          json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionSection;
          json_cfg.recoverSelection = 2;
          json_cfg.setUnionSelection = 0;
          thinkEditor.ParseDocs('病程记录', '首次病程记录-1-4', xml4, json_cfg);

          //thinkEditor.SetViewMode(E_VIEW_MODE.Edit, {});

          let printCfg = {};
          printCfg.printMode = 1; // 0：视图打印 1：矢量打印
          printCfg.view = {
            mode: E_VIEW_MODE.Print,
            hideElementHint: true
          };

          //[0] 注意await,后续可能关闭文档或编辑器
          await thinkEditor.PrintDocAsync(printCfg);

          //[1]如果ThinkEditor还需使用，当前文档不需使用了，则关闭文档
          //thinkEditor.CloseDoc(docName);
          //或;
          //thinkEditor.CloseAllDoc();

          //[2]如果ThinkEditor不再使用了，则释放编辑器及内存
          console.warn('thinkEditor.UnInit()'); //必要，释放内存
          thinkEditor.UnInit();
        }
        break;
      case 'UnionTimeTest':
        {
          var url = '/system/emr/AdmissionRecord.xml'; //"/system/test/pacsdemo.jpg";
          let response = await fetch(url, {
            credentials: 'same-origin'
          });
          let xml1 = await response.arrayBuffer();

          json_cfg.organizeMode = 2;
          json_cfg.recoverSelection = 2;
          json_cfg.setUnionSelection = 0;

          console.time('UnionTimeTest1');
          for (var i = 0; i < 100; i++) {
            this.thinkEditor.ParseDocs('0', i + '', xml1, json_cfg);
          }
          console.timeEnd('UnionTimeTest1');

          console.time('UnionTimeTest2');
          this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, {});
          console.timeEnd('UnionTimeTest2');
        }
        break;
      case 'SetFontType':
        this.thinkEditor.SetFontType('黑体');
        break;
      case 'SetWaterMarkConfig':
        var attach_id = 'waterMarkTest';
        json_cfg.fontName = '宋体';
        json_cfg.size = 1.0;
        json_cfg.Fragment = '文字水印测试';

        json_cfg.angle = 315;

        json_cfg.forbidPrint = 0;
        json_cfg.repeat = 1;
        json_cfg.fillMode = 0;

        json_cfg.topPadding = 2.8;
        json_cfg.bottomPadding = 2.8;
        json_cfg.leftPadding = 2.8;
        json_cfg.right_Padding = 2.8;

        //json_cfg.top_padding = 1.8;
        //json_cfg.bottom_padding = 1.8;
        //json_cfg.left_padding = 1.8;
        //json_cfg.right_padding = 1.8;

        this.thinkEditor.SetWaterMarkConfig(attach_id, json_cfg);
        break;
      case 'SetUserDefinedMenu':
        //var json_str = `[{"type":"user_defined1","text":"自定义菜单1","code":0},{"type":"user_defined2","text":"自定义菜单2","code":2}]`;
        var json_str = `[]`;
        var json_obj = JSON.parse(json_str);
        this.thinkEditor.SetUserDefinedMenu(json_obj);
        break;
      default:
        break;
    }

    return;
    /*
        this.thinkEditor.SetElementSelectDateTime("name", E_TIME_TYPE.DateTime);
        */
    /*
        const json = {};
        json.srcDocName = "Compare1";
        json.destDocName = "Compare2";

        var url = "/system/emr/Compare1.xml";//"/system/test/pacsdemo.jpg";
        let response = await fetch(url,  {credentials:'same-origin'});
        let src_data = await response.arrayBuffer();
        url = "/system/emr/Compare2.xml";//"/system/test/pacsdemo.jpg";
        response = await fetch(url,  {credentials:'same-origin'});
        let dest_data = await response.arrayBuffer();
        this.thinkEditor.SetCompareDocs(json,src_data,dest_data);
        return;
*/
    /*
        const json1 = {};
        json1.ids = [];
        json1.ids[0] = "asdf";
        json1.ids[1] = "test";
        this.thinkEditor.SelectAnnotates(json1);
        return;
*/
    /*
        var json_obj={};
        json_obj.locked = 1;
        //json_obj["top-border-style"] = 0;
        this.thinkEditor.SetHeaderConfig(json_obj);
        return;*/
    /*
        var json = {};
        json.hide = true;
        //json.display_revise_del = false;
        //json.display_qualitycontrol_infobox
        await this.thinkEditor.SetParagraphConfig("主诉", json);
        return;    */
    /*
       this.thinkEditor.SetElementContent("6CAEC966-0CC7-4C7D-ACFC-8436704A4708", "5689");
       this.thinkEditor.GetDoc(E_DOC_FORMAT.XML,E_DOC_TYPE.Entity);
       return;    */
    /*var json_cfg ={};
        json_cfg.check_figure_style =0;
        json_cfg.multiple_choice =1;
        this.thinkEditor.InsertCheckBoxGroup("1234", "病史", "吸烟史", json_cfg);
*/
    /*
    var json_cfg ={};
    //json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.MergeContent;
    json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionContent;
    //json_cfg.organizeMode = E_DOCS_ORGANIZE_MODE.UnionSection

    this.thinkEditor.CreateDoc("frist");

    this.thinkEditor.SetEditorInfo("pc", "zltest","zl",1,"");

    var url = "/system/test/merge_test/JumpDemo.xml";//"/system/test/pacsdemo.jpg";
    let emr_response = await fetch(url,  {credentials:'same-origin'});
    let data = await emr_response.arrayBuffer();
    this.thinkEditor.ParseDocs("frist", "", data, json_cfg);

    var url = "/system/test/merge_test/wg20220211.xml";//"/system/test/pacsdemo.jpg";
    emr_response = await fetch(url,  {credentials:'same-origin'});
    data = await emr_response.arrayBuffer();
    this.thinkEditor.ParseDocs("frist", "1", data, json_cfg);

    var url = "/system/test/merge_test/wg20220208.xml";//"/system/test/pacsdemo.jpg";
    emr_response = await fetch(url,  {credentials:'same-origin'});
    data = await emr_response.arrayBuffer();
    this.thinkEditor.ParseDocs("frist", "2", data, json_cfg);


    this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit,{});

    return;*/
    /*
    this.thinkEditor.SetParagraphListMode(E_LIST_MODEL.OrderListModel_5);
    return;*/
    /*
        var url = "/system/test/668888.jpg";//"/system/test/pacsdemo.jpg";
        //var thinkEditorDemo = this;
        let response = await fetch(url,  {credentials:'same-origin'});
        let img_data = await response.arrayBuffer();
        var img_name = "668888.jpg";
        var img_md5 = this.thinkEditor.SetImageResource(img_name, img_data, img_data.length);
        console.log(img_md5);


        var attach_id = "waterMarkImg";

        let json_cfg={};
        json_cfg.image_name = img_name;
        json_cfg.md5 = img_md5;
        json_cfg.alpha = 0xff;
        json_cfg.forbid_print = 1;
        json_cfg.repeat = 0;
        json_cfg.fill_mode = 1;

        json_cfg.top_padding = 0;
        json_cfg.bottom_padding = 0;
        json_cfg.left_padding = 0;
        json_cfg.right_padding = 0;

        //json_cfg.top_padding = 1.8;
        //json_cfg.bottom_padding = 1.8;
        //json_cfg.left_padding = 1.8;
        //json_cfg.right_padding = 1.8;

        this.thinkEditor.SetWaterMarkConfig(attach_id, json_cfg);
*/
    /*
    let attach_cfg = new AttachCfg();
    attach_cfg.layer_ = 0;
    attach_cfg.type_ = 1;
    attach_cfg.source_ = 0;
    attach_cfg.forbid_print_ = 1;
    attach_cfg.texture_type_ = 0;
    attach_cfg.repeat_ = 1;
    let ret1 = this.thinkEditor.SetWaterMarkImage(attach_id,0xff, 0, 0,attach_cfg,img_name, img_md5);
    console.log(ret1);
    */
    //let ret2 = this.thinkEditor.SetWaterMarkPadding(attach_id,1.8, 1.8,1.8, 1.8);
    // console.log(ret2);

    /*
        var editor_config={};
        //editor_config.debug_flush = true;
        editor_config.disable_edit_limit = true;
        this.thinkEditor.SetEditorConfig(editor_config);
*/
    /*
         var json = {};
         var commnet_id = this.thinkEditor.InsertComment("","", json);
         var tt = commnet_id;
         */
    /*
        var json = {};
        json.trigger_type = 0;
        json.execute_mode = 0;
        json.action = `this.element.value=='吸烟' ? smoke.element.visible=true:smoke.element.visible=false`;

        await this.thinkEditor.SetElementConfig("", json);
*/
    /*
var json = {};
json.forbid_series_split = true;
json.split_paragrah_mode = 2;
//json.display_revise_del = false;
//json.display_qualitycontrol_infobox
await this.thinkEditor.SetParagraphConfig("123", json);

*/
    /*
            var editor_config={};
    //editor_config.debug_flush = true;
    editor_config.debug_event = true;
    editor_config.log_level = 10;
    this.thinkEditor.SetEditorConfig(editor_config);
*/

    /*
var json = {};
json.delete_mode = true;
json.split_paragrah_mode = 1;
//json.display_revise_del = false;
//json.display_qualitycontrol_infobox
this.thinkEditor.SetElementConfig("", json);
        //this.thinkEditor.SetElementBase("", "设置-测试", "", "{", "}", "", "");
        //const cfg = new SearchReplaceCfg();
//const aa = this.thinkEditor.GetObjectFragment(E_IDENTITY_OBJECT_TYPE.Paragraph, "103",E_DOC_FORMAT.XML, cfg);
*/
    /*
thinkEditor.DisplayImageSetWindow(1);
//return;*/
    //this.thinkEditor.DisplayElementSetWindow(1);
    /*
    this.thinkEditor.DisplaySearchReplaceWindow(1);
           return;  */

    /*
//var json = {};
//json.display_revise_auxiliary = true;
//json.display_revise_del = false;
//json.display_qualitycontrol_infobox = false;
//json.display_comment_infobox = false;
json.display_carriage_return = false;
json.display_trim_marks = true;
this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, json);
        //this.thinkEditor.CloseAllDoc();
        //this.thinkEditor.DisplayEditToolBar(E_DISPLAY_MODE.Hide);
        return;
        var init_config={
        doc_rename: "new1111", // #2e83f8

        };
	    this.thinkEditor.SetDocConfig("", init_config);
*/

    /*
	//var signImage = "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";
	var md5 = this.thinkEditor.SetImageResourceBase64("sign.bmp", signImage, signImage.length, "");// (E_VIEW_MODE::Edit);
	var signFragment = "<Fragment><Image width='2' height='0.8' file='当前医师电子签名.jpg' md5='" + md5 + "'/></Fragment>";

	this.thinkEditor.SetElementContent("sign", signFragment);
*/
    //this.handleFullScreen();
    //var doc_name = this.thinkEditor.GetVisibleDocName();
    //this.thinkEditor.CloseDoc(doc_name);

    /*
       //---------------
        var init_config={
        tab_color: "FF2E83F9", // #2e83f8
       //tab_hovered_color: "FF83BDFF", // #82beff
       //tab_active_color: "FF0076FF", //
        };
        //init_config.tab_color = "ffff0000";//0xffff0000;
	    this.thinkEditor.SetEditorConfig(init_config);
*/

    //this.thinkEditor.ResizeViewPanel(1000,1000);
    //---------------
    //this.thinkEditor.CloseAllDoc();

    //---------------
    //var ret =  this.thinkEditor.SetSelectContentLock(true);
    //var ret =  this.thinkEditor.InsertElement("test", "请输入", "", "[", "]", "", "");
    //var test = this.thinkEditor.GetSelectRangeContent(E_DOC_FORMAT.TEXT);
    //---------------
    /*
    var json_obj={};
    json_obj.display = 0;
    //json_obj["top-border-style"] = 0;
    this.thinkEditor.SetHeaderConfig(json_obj);
    this.thinkEditor.SetFooterConfig(json_obj);
    */
    //-------------------
    //this.thinkEditor.GetSelectDocName();
    //var editorContainer = this.$refs.editorContainer;
    //editorContainer.width = 1000;
    //editorContainer.height = 1000;
    // this.thinkEditor.ResizeViewPanel(1000,1000);
    //var ret = this.thinkEditor.SetElementContent("简要诊断", '<Fragment><Format size=\"0.776\" />XXX人民医院</Fragment>');
    //---------------
    //var cfg = new SeparatorCfg();
    //cfg.all = 0;
    //cfg.independent_line_ = true;

    //---------------
    // this.thinkEditor.DisplayBarCodeSetWindow(E_DISPLAY_MODE.Show,E_SET_MODE.InsertSpecial);
    //this.thinkEditor.DisplayImageSetWindow(E_DISPLAY_MODE.Show,E_SET_MODE.SM_INSERT_COMMON);

    //---------------
    //var docname = this.thinkEditor.GetSelectDocName();
    //console.log(docname);
    //docname = this.thinkEditor.GetVisibleDocName();
    //console.log(docname);
    //---------------
    //      this.thinkEditor.SetParagraphAlignContent(E_ALIGN_HORIZONTAL_MODE.Center);
    //---------------
    // var outline = this.thinkEditor.GetOutline();
    // var gotoOutline =`{\"type\":\"Section\",\"index\":0,\"content\":{\"type\":\"Main\",\"Paragraph\":{\"type\":\"Paragraph\",\"id\":\"主诉\",\"name\":\"\",\"level\":0,\"index\":3,\"member\":{\"type\":\"Element\",\"id\":\"简要诊断\",\"name\":\"\"}}}}`;
    //var ret = this.thinkEditor.GotoOutline(gotoOutline);

    //---------------
    //this.thinkEditor.DisplayElementSetWindow(E_DISPLAY_MODE.Show,E_SET_MODE.SM_INSERT_COMMON);
    //---------------
    //this.thinkEditor.SetParagraphSpaceBefore(1);
    //---------------
    //this.thinkEditor.SetFontFerrule(E_FERRULE_MODE.Circle);
    //---------------
    //this.thinkEditor.SetBackgroundColor(this.$BuildTEColorByStr("rgb(66, 133, 244)"));
  }

  /*
     SetViewMode
CancelCompareDocs
Paste
Exit
CloseAllDoc
     */

  Test2() {
    //this.thinkEditor.SetDocConfig('', { testBreak: 0 });
    this.thinkEditor.SetParagraphConfig('nodraw2', {
      hide: true
    });
    return;
    var testCode = 'CloseAllDoc';
    switch (testCode) {
      case 'CloseAllDoc':
        this.thinkEditor.CloseAllDoc();
        break;
      case 'GetDocProperties':
        let v = this.thinkEditor.GetDocProperties();
        break;
      case 'SetDocConfig':
        this.thinkEditor.SetDocConfig('', { disableParagraphLockedTip: 0 });
        break;
      case 'testDump1':
        this.thinkEditor.SetEditorConfig({ testDump1: 1 });
        break;
      case 'Exit':
        this.thinkEditor.Exit();
        break;
      case 'Paste':
        this.thinkEditor.Paste(E_PASTE_TYPE.Normal);
        break;
      case 'SetViewMode':
        this.thinkEditor.SetViewMode(E_VIEW_MODE.Print, {});
        break;
      case 'CancelCompareDocs':
        var json_cfg = {};
        json_cfg.retainDocs = true;
        this.thinkEditor.CancelCompareDocs(json_cfg);
        break;
      default:
        break;
    }

    return;
    /*
            this.thinkEditor.SetElementContent("6CAEC966-0CC7-4C7D-ACFC-8436704A4708", "5689");
            this.thinkEditor.GetDoc(1,0);
            return;
            */
    /*
this.thinkEditor.DisplayImageEdit(1);
return;*/

    var json = {};
    /*
 json.display_edit_auxiliary = true;//change
 json.display_selection_effect = true;
 json.display_revise_auxiliary = true;
 json.display_revise_del = true;
 json.display_qualitycontrol_infobox = false;
 json.display_comment_infobox = true;
*/
    json.displayCommentMode = 2;
    //json.display_trim_marks = false;
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit, json);
    //this.thinkEditor.DisplayEditToolBar(E_DISPLAY_MODE.Hide);
    return;
    var json_obj = {};
    json_obj.display = 1;
    //json_obj["top-border-style"] = 0;
    this.thinkEditor.SetHeaderConfig(json_obj);
    this.thinkEditor.SetFooterConfig(json_obj);
  }
  SetUserDefinedMenuDemo() {
    var json_str = `[{"type":"user_defined1","text":"自定义菜单1","code":0},{"type":"user_defined2","text":"自定义菜单2","code":2}]`;
    var json_obj = JSON.parse(json_str);
    this.thinkEditor.SetUserDefinedMenu(json_obj);
  }
  SetViewModeShowReviseEdit(thinkEditor) {
    thinkEditor.SetEditorInfo('pc', '12666', 'admin', 1, { note: '上级医师审阅修改' });
    thinkEditor.SetEditMode(true, true, true, true);
    thinkEditor.SetViewMode(E_VIEW_MODE.Edit, { displayReviseAuxiliary: true, displayReviseDel: true });
  }
  SetViewModeHideReviseEdit(thinkEditor) {
    thinkEditor.SetEditorInfo('pc', '12666', 'admin', 1, { note: '上级医师审阅修改' });
    thinkEditor.SetEditMode(true, true, true, true);
    thinkEditor.SetViewMode(E_VIEW_MODE.Edit, { displayReviseAuxiliary: false, displayReviseDel: false });
  }
  SetViewModeNullReviseEdit(thinkEditor) {
    thinkEditor.SetEditorInfo('pc', '12666', 'admin', 1, { note: '上级医师审阅修改' });
    thinkEditor.SetEditMode(false, false, false, false);
    thinkEditor.SetViewMode(E_VIEW_MODE.Edit);
  }
  CancelSign() {
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit);
    this.thinkEditor.SetElementContent('sign', '');
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Browse);
  }

  async SelectImage() {
    var fileData = await PopFileSelector('.jpg,.svg,.bmp,.png,');
    var data = fileData.data;
    var imgName = fileData.fileName;
    this.thinkEditor.InsertImage('', 0, 0, imgName, data, data.byteLength, {});
  }
  RandomString(len) {
    len = len || 32;
    var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
    var maxPos = $chars.length;
    var pwd = '';
    for (var i = 0; i < len; i++) {
      pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  }
  async OpenDoc() {
    var docData = await PopFileSelector('.xml,');
    var docName = docData.fileName; //this.RandomString(9);
    var data = docData.data;
    /************************
     * 方案一：简化示例 没有加载资源
     * *************************/
    //this.thinkEditor.CreateDoc(doc_name);
    //this.thinkEditor.ParseDoc(doc_name, data, E_DOCS_ORGANIZE_MODE.Single);
    //this.thinkEditor.SetDisplayScale(E_PAGES_LAYOUT_MODE.Ratio, 1.2);
    //this.thinkEditor.SetViewMode(E_VIEW_MODE.Edit);
    /************************
     * 方案二：通用演示
     * *************************/
    this.OpenEmrCommon(docName, data);
  }
  NewTemplateDoc() {
    var doc_name = this.RandomString(9);
    this.OpenEmr(doc_name, E_DOC_TYPE.Template, 'empty.xml');
    this.PreLoadTemplateSource();
  }
  async NewEntityDoc() {
    //this.thinkEditor.CloseAllDoc();
    var doc_name = this.RandomString(9);
    await this.OpenEmr(doc_name, E_DOC_TYPE.Entity, 'empty.xml');
    /*
            const new_doc =`<Doc>
     <Sections>
      <Section>
       <Main>
        <Paragraph id="1212">
        段落内容
        </Paragraph>
       </Main>
      </Section>
     </Sections>
     <Resources />
    </Doc>`

    const json_cfg={};
    json_cfg.replace_main_content = true;
    this.thinkEditor.AppendFragment('',new_doc, json_cfg);

    const json_p_cfg={};
    json_p_cfg.forbid_series_split =true;
    json_p_cfg.split_paragrah_mode =2;
    this.thinkEditor.SetParagraphConfig("1212",json_p_cfg);
    */
  }
  async NewTimeAxis() {
    var url = '/system/emr/TemperatureTimeAxis.xml'; //"/system/test/pacsdemo.jpg";
    //var thinkEditorDemo = this;
    let response = await fetch(url, {
      credentials: 'same-origin'
    });
    let data = await response.arrayBuffer();
    //response.arrayBuffer().then(data =>  {
    var doc_name = '体温单' + ((Math.random() * 1000000) | 0);
    this.OpenEmrCommonHandle(doc_name, data);

    var d = new Date();
    var timeS = parseInt(d.getTime() / 1000);

    var json = {};
    json['id'] = '';
    json['startTime'] = timeS; //注意传float 编辑器json会读取不到数据，需使用parseInt
    json['endTime'] = timeS + 6 * 86400; //当前页面宽度 6天为一页
    this.thinkEditor.SetTimeGridConfig(json);
    this.thinkEditor.UpdateTimeAxis('');
    this.thinkEditor.SetViewMode(E_VIEW_MODE.Browse);
  }
  SaveToPdfView() {
    var dataBuffer = this.thinkEditor.GetPagesPDF(0, 10, 1.5, 0);
    ShowSaveFileDialog('thinkeditor.pdf', dataBuffer);
  }
  async SaveToPdf() {
    var dataBuffer = await this.thinkEditor.GetPDF({});
    ShowSaveFileDialog('thinkeditor.pdf', dataBuffer);
  }
  SaveToXml(docType = E_DOC_TYPE.Entity) {
    //step1: 获取文档的xml
    var docData = this.thinkEditor.GetDoc(E_DOC_FORMAT.XML, docType, { formatPretty: true });
    //step2: 应用对xml的使用（保存到本地或数据库）。当前示例：保存到本地
    ShowSaveFileDialog('thinkeditor.xml', docData);
  }
  SaveToXmlTemplate() {
    var docData = this.thinkEditor.GetDoc(E_DOC_FORMAT.XML, E_DOC_TYPE.Template, { formatPretty: true });
    ShowSaveFileDialog('thinkeditorTemplate.xml', docData);
  }
  SaveToDocText() {
    var docData = this.thinkEditor.GetDoc(E_DOC_FORMAT.TEXT, E_DOC_TYPE.Entity, {});
    ShowSaveFileDialog('thinkeditorDocText.txt', docData);
  }
  SaveToDocJson() {
    var outlineObj = this.thinkEditor.GetOutline({ mode: 2 });
    ShowSaveFileDialog('thinkeditorDocJson.json', JSON.stringify(outlineObj));
  }
  PrintDoc() {
    let printCfg = {};
    printCfg.printMode = 1; //0:视图打印 1：矢量打印
    printCfg.view = {
      mode: E_VIEW_MODE.Print,
      hideElementHint: true
    };
    this.thinkEditor.PrintDoc(printCfg);
  }
  SaveToHtml() {
    var dataBuffer = this.thinkEditor.GetDoc(E_DOC_FORMAT.HTML, E_DOC_TYPE.Entity);
    ShowSaveFileDialog('thinkeditor.html', dataBuffer);
  }
  SaveToImg() {
    var dataBuffer = this.thinkEditor.GetPagesImage(E_IMAGE_TYPE.JPG, 0, 2, 1);
    ShowSaveFileDialog('thinkeditor.jpg', dataBuffer);
  }
  async InsertImage() {
    var url = '/system/test/sign.svg'; //"/system/test/pacsdemo.jpg";
    let response = await fetch(url, {
      credentials: 'same-origin'
    });
    let data = await response.arrayBuffer();
    await this.thinkEditor.InsertImage('', 0, 0, 'pacsdemo', data, data.byteLength, {});
  }
  async LoadSystemKnowledge() {
    var json_str = `[
	{
		"class": "系统123",
		"Options": [
			{
				"id": "属地567",
				"multiple_choice": false,
				"group_exclusion": true,
				"Items": [
					{
						"id": "本县区888",
						"group-id": "",
						"weight": 1.00,
						"content": "*fragment_base64*"
					},
					{
						"id": "本市其他县区",
						"group-id": "",
						"weight": 1.00
					},
					{
						"id": "本省其他地市",
						"group-id": "",
						"weight": 1.00
					},
					{
						"id": "外省",
						"group-id": "",
						"weight": 1.00
					},
					{
						"id": "港澳台",
						"group-id": "",
						"weight": 1.00
					},
					{
						"id": "外籍",
						"group-id": "",
						"weight": 1.00
					}
				]
			}
		]
	}
]`;
    let thinkEditor = this.thinkEditor;
    var json_obj = JSON.parse(json_str);
    thinkEditor.AddSystemKnowledge(json_obj);
    /************************************
     * 加载 系统知识库，比如姓别 省份
     * ***********************************/

    var url = '/system/knowledge/System/System.xml';
    let response_system = await fetch(url, {
      credentials: 'same-origin'
    }); //.then(function (response) {
    let data_system = await response_system.arrayBuffer();
    thinkEditor.AddSystemKnowledge(data_system);

    /************************************
     * 加载通用知识库
     * ***********************************/

    var url = '/system/knowledge/Common/CommonSymptoms.xml';
    let response_common = await fetch(url, {
      credentials: 'same-origin'
    }); //.then(function (response) {
    let data_common = await response_common.arrayBuffer();
    thinkEditor.AddSystemKnowledge(data_common);
  }
  async LoadDocKnowledge(doc_class) {
    /************************************
     * 加载科室知识库
     * ***********************************/
    let thinkEditor = this.thinkEditor;
    if (doc_class == '呼吸科') {
      var url = '/system/knowledge/Respiratory/Respiratory.xml';
      let response = await fetch(url, {
        credentials: 'same-origin'
      });
      let data = await response.arrayBuffer();
      thinkEditor.AddDocKnowledge(doc_class, data);
    } else if (doc_class == '感染科') {
      var url = '/system/knowledge/InfectiousDisease/InfectiousDiseaseCard.xml';
      let response = await ffetch(url, {
        credentials: 'same-origin'
      });
      let data = await response.arrayBuffer();
      thinkEditor.AddDocKnowledge(doc_class, data);
    }
  }
  InsertFormula() {
    this.thinkEditor.InsertFormula({ id: '', style: E_FORMULA_STYLE.MensesStyle_1, alignVerticalLayout: E_LAYOUT_VERTICAL_ALIGN.OtherMiddle });
  }
  InsertCheckBox() {
    this.thinkEditor.InsertCheckBox({ id: '', weight: 0, content: '选框示例', checkFigureStyle: E_CHECK_FIGURE_STYLE.STYLE_1 });
  }
  InsertQRCode() {
    this.thinkEditor.InsertBarCode({ id: '', width: 1.5, height: 1.5, data: Base64.encode('123456789'), type: E_BARCODE_TYPE.QRCODE });
  }
  InsertBarCode() {
    this.thinkEditor.InsertBarCode({ id: '', width: 3, height: 1.5, data: Base64.encode('123456789'), type: E_BARCODE_TYPE.CODE128 });
  }
  InsertElement() {
    //this.thinkEditor.InsertElement("test", "请输入", "", "[", "]", "体温", "C");
    //"test", "请输入", "", "[", "]", "", ""
    let jsonCfg = {
      id: 'test',
      hint: '请输入',
      tip: '',
      beginBorder: '[',
      endBorder: ']',
      beforeTag: '',
      afterTag: ''
    };
    this.thinkEditor.InsertElement(jsonCfg);

    var ele_cfg = new ElementCfg();
    ele_cfg.dynamic_load_ = false;
    //示例：设置引用源
    this.thinkEditor.SetElementSource('test', '基本资料', '医保号', ele_cfg);

    //示例：设置选项输入
    ele_cfg.multiple_choice_ = false;
    //this.thinkEditor.SetElementSelectOption("", "系统", "良好程度", "",ele_cfg);
    //this.thinkEditor.SetElementSelectOption("", "系统123", "属地567", "",ele_cfg);
    //this.thinkEditor._SetEditorCanvasFoucs(true);
    //this.Module.canvas = canvasObj;
  }

  async InsertFragment(fragment_file) {
    var url = '/system/fragment/' + fragment_file;
    let response = await fetch(url, {
      credentials: 'same-origin'
    });
    let data = await response.arrayBuffer();
    this.thinkEditor.InputFragment(data);
  }
  async AppendFragment(fragment_file) {
    var url = '/system/fragment/' + fragment_file;
    let response = await fetch(url, {
      credentials: 'same-origin'
    });
    let data = await response.arrayBuffer();
    //this.thinkEditor.ParseDoc("",data, E_DOCS_ORGANIZE_MODE.UnionContent);
    var json_cfg = {};
    //json_cfg.replace_main_content = true;
    this.thinkEditor.AppendFragment('', data, json_cfg);
  }

  PreLoadEntitySourceData() {
    let thinkEditor = this.thinkEditor;
    thinkEditor.SetSource('认证', '机构名', "<Fragment><Format size='0.776' />江东第一人民医院</Fragment>");

    /********************病人基础资料*************************/
    thinkEditor.SetSource('基本资料', '姓名', '张小北');
    thinkEditor.SetSource('基本资料', '性别', '男');
    thinkEditor.SetSource('基本资料', '身份证', '5131246845698xxxxx');
    thinkEditor.SetSource('基本资料', '年龄', '23');
    thinkEditor.SetSource('基本资料', '民族', '汉');
    thinkEditor.SetSource('基本资料', '医保号', '1982309');

    //--------
    //this.thinkEditor.SetSource("门诊资料", "门诊号", "237899");
    thinkEditor.SetSource('门诊资料', '门诊号', "<Fragment><BarCode width='1.50' height='1.50' cfg='13a'>MTIzNDU2Nzg5</BarCode></Fragment>"); // "237899");

    thinkEditor.SetSource('住院资料', '住院号', 'new163679'); //163679
    thinkEditor.SetSource('住院资料', '科室', '骨科');
    thinkEditor.SetSource('住院资料', '病房', '6');
    thinkEditor.SetSource('住院资料', '床号', '16');
    thinkEditor.SetSource('住院资料', '入院时间', '2019-09-09 09:16:23');

    /***********************************
     * 设置图片源-此处用作签名
     * md5为图片二进制数据对应的md5码
     * 注意：图片数据在"imageResource.request"事件中调用SetImageResource/SetImageResourceBase64设置
     * 详见《设计手册-图像》
     * ************************************/
    var md5 = 'a04cfa91f403f799ce07252e85bda12b'; //md5必需为图片真实md5值
    var signSource = "<Fragment><Image width='2' height='0.8' file='当前医师电子签名.jpg' md5='" + md5 + "'/></Fragment>";
    thinkEditor.SetSource('医师资料', '电子签名', signSource);
    /*******************上级医师********************/
    md5 = 'b3aa75a6c69fb17ec3cc4290bbea8a57'; //md5必需为图片真实md5值
    signSource = "<Fragment><Image width='2' height='0.8' file='审核医师电子签名.jpg' md5='" + md5 + "'/><Format color='c93756' size='0.6'/>/</Fragment>";
    thinkEditor.SetSource('医师资料', '审核医师签名', signSource);
  }
  /**
   * 预设空数据源，用于编辑模板时元素-源类型选框中有对应数据
   * SetSource的source-class、source-id参数都是由应用自行规划取名和分类的
   * @memberof ThinkEditorDemo
   */
  PreLoadTemplateSource() {
    console.log('PreLoadTemplateSource');
    var thinkEditor = this.thinkEditor;
    thinkEditor.SetSource('认证', '机构名', '');
    /********************病人基础资料*************************/
    thinkEditor.SetSource('基本资料', '姓名', '');
    thinkEditor.SetSource('基本资料', '性别', '');
    thinkEditor.SetSource('基本资料', '身份证', '');
    thinkEditor.SetSource('基本资料', '年龄', '');
    thinkEditor.SetSource('基本资料', '民族', '');
    thinkEditor.SetSource('基本资料', '医保号', '');

    //--------
    thinkEditor.SetSource('门诊资料', '门诊号', '');

    thinkEditor.SetSource('住院资料', '住院号', '');
    thinkEditor.SetSource('住院资料', '科室', '');
    thinkEditor.SetSource('住院资料', '病房', '');
    thinkEditor.SetSource('住院资料', '床号', '');
    thinkEditor.SetSource('住院资料', '入院时间', '');
    /***********************************
     * 设置图片源-此处用作签名
     * md5为图片二进制数据对应的md5码
     * 注意：图片数据在"imageResource.request"事件中调用SetImageResource/SetImageResourceBase64设置
     * 详见《设计手册-图像》
     * ************************************/
    var md5 = 'a04cfa91f403f799ce07252e85bda12b'; //md5必需为图片真实md5值
    var signSource = "<Fragment><Image width='2' height='0.8' file='当前医师电子签名.jpg' md5='" + md5 + "'/></Fragment>";
    thinkEditor.SetSource('医师资料', '电子签名', signSource);
    /*******************上级医师********************/
    md5 = 'b3aa75a6c69fb17ec3cc4290bbea8a57'; //md5必需为图片真实md5值
    signSource = "<Fragment><Image width='2' height='0.8' file='审核医师电子签名.jpg' md5='" + md5 + "'/><Format color='c93756' size='0.6'/>/</Fragment>";
    thinkEditor.SetSource('医师资料', '审核医师签名', signSource);
  }

  /**
   * 供应用参考的封装打开文档的方法
   *
   * @param {string} doc_name
   * @param {string or ArrayBuffer} data
   * @param {E_DOC_TYPE} doc_type
   * @memberof ThinkEditorDemo
   */
  OpenEmrCommonHandle(doc_name, data, doc_type, enableRevise) {
    let cluster_name = doc_name;
    let thinkEditor = this.thinkEditor;

    thinkEditor.CreateDoc(cluster_name);
    thinkEditor.SelectDoc(cluster_name);
    //thinkEditor.VisibleDoc(cluster_name);
    /*V2版本 改为 CreateDocEditor后，加载对应源到DocEditorEntity
    if (doc_type == E_DOC_TYPE.Template) {
      //note:编辑模板前需预设空Source
      this.PreLoadTemplateSource();
    } else if (doc_type == E_DOC_TYPE.Entity) {
      //note:编辑文档实体时，无需预设Source, 而是动态获取，详见setElementContent.request事件处理
      this.PreLoadEntitySourceData(); //@20220610为演示方便，增加加载签名Source
    }

    */
    /*
        必需先解析ParseDoc文档中的编辑信息，再加载此次的编辑者信息
    */
    //thinkEditor.SetEditorInfo("pc", "12666", "admin", 0, {"note":"上级医师审阅修改"});//考虑到json_str xml_str存放可以扰乱xml问题attach_str=json_str
    /*
        SetEditorInfo的attrs_json参数和格式由用户自定义
    */
    var attrs_json = {};
    attrs_json.note = '上级医师审阅修改';
    thinkEditor.SetEditorInfo('pc', '12666', 'admin', 1, attrs_json); //考虑到json_str xml_str存放可以扰乱xml问题attach_str=json_str

    /*
    enable_permission:[是否可删除]启用文档内容安全和权限控制。默认为false文档内容可任意编辑。
    enable_revise:[是否逻辑删除 新增内容带痕]使能审阅模式。默认为false，正常编辑
    modify_same_level:能否修改或删除同授权等级的内容,默认为true
    force_modify: 高权限的是否可以修改低权限禁止删除、或禁止编辑的内容。 默认为false不可修改
    modify_owner_old_by_revise: 是否带痕修改自己曾经输入的内容，默认false,可直接删除自己写的旧内容
    */
    //thinkEditor.SetEditMode(true, true, true, true);

    //在加载文档之后，可修改

    thinkEditor.SetDocType(doc_type);

    thinkEditor.ParseDoc(cluster_name, data, {});

    if (enableRevise == 1) {
      thinkEditor.SetEditMode(true, true, true, true);
    } else {
      thinkEditor.SetEditMode(false, false, false, false);
    }

    thinkEditor.SetDocClass('呼吸科');
    thinkEditor.SetDisplayScale(E_PAGES_LAYOUT_MODE.Ratio, 1.0);

    //取得该文档需要的源信息
    //thinkEditor.SetViewMode(E_VIEW_MODE.Edit);
  }
  OpenEmrCommon(doc_name, data, doc_type, view_mode, enableRevise) {
    let thinkEditor = this.thinkEditor;

    this.OpenEmrCommonHandle(doc_name, data, doc_type, enableRevise);

    // if (this.thinkEditor._IsNull(view_mode)) {
    //   view_mode = E_VIEW_MODE.Edit;
    // }

    thinkEditor.SetViewMode(view_mode);

    var end1 = new Date().getTime();

    /*  //@20220226 测试痕迹
     const json = {};
     json.display_revise_auxiliary = true;
     json.display_revise_del = false;
     json.display_qualitycontrol_infobox = false;
     json.display_comment_infobox = false;
     json.display_edit_auxiliary = true;
     json.display_selection_effect = true;
     json.display_carriage_return = false;
     this.thinkEditor.SetViewMode(view_mode, json);//E_VIEW_MODE.Edit*
     */
  }

  /**
   * 示例：通过http获取病历并打开
   *
   * @param {string} doc_name
   * @param {E_DOC_TYPE} doc_type
   * @param {string} doc_file
   * @param {E_VIEW_MODE} view_mode
   * @memberof ThinkEditorDemo
   */
  async OpenEmr(doc_name, doc_type, docData, viewMode, enableRevise) {
    if (typeof doc_type === 'undefined') {
      doc_type = E_DOC_TYPE.Entity;
    }

    await this.OpenEmrCommon(doc_name, docData, doc_type, viewMode, enableRevise);
    let thinkEditor = this.thinkEditor;
    thinkEditor.SetDefaultFont('宋体', '小五');
  }
}
