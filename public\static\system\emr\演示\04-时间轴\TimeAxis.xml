<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' class='呼吸科' pagesCount='2'>
	<Sections>
		<Section cfg='9' pagesCount='2' width='21.000' height='29.700' borderWidth='0.020' topPadding='2.540' bottomPadding='0.500' leftPadding='0.800' rightPadding='0.800'>
			<Attach backColor='ffffff' />
			<Header topMargin='0.200' topBorderWidth='0.020' leftBorderWidth='0.020' rightBorderWidth='0.020'>
				<Paragraph xCfg='2' spaceAfter='0.100' lineSpaceValue='0.600'>
					<Font size='0.564' />山<Space count='2' />东<Space count='2' />X<Space count='2' />X<Space count='2' />医<Space count='2' />院<Font size='0.423' />
				</Paragraph>
				<Paragraph>
					<Font size='0.635' />体<Space count='2' />温<Space count='2' />单<Font size='0.422' />
				</Paragraph>
				<Paragraph xCfg='0' spaceAfter='0.050'>
					<Font size='0.370' />姓名：<Element cfg='30000' hint='住院资料:姓名' width='1.500'>张XX</Element>性别：<Element cfg='30000' inputMode='2' optionClass='系统' optionId='性别' linkStr='、' hint='住院资料:姓名' width='1.000'>女</Element>年龄：<Element cfg='30000' hint='请输入'>32</Element>
					<Space />岁<Space count='2' />科室：<Element cfg='30000' hint='请输入' width='2.000'>骨科</Element>床号：<Element cfg='30000' hint='住院资料:住院号' width='1.000'>6</Element>病案号：<Element cfg='30000' hint='住院资料:住院号' width='1.500'>163679</Element>入院日期：<Element cfg='30000' inputMode='3' time='2021-12-04 00:00:00' hint='住院资料:住院号' width='2.000'>2021-12-04</Element>
					<Font size='0.422' />
				</Paragraph>
			</Header>
			<Footer bottomMargin='0.500' borderWidth='0.020'>
				<Paragraph xCfg='2' lineSpaceValue='0.600'>
					<PageNum xCfg='4' width='2.959' height='0.506' lCfg='2'>
						<Unit eCfg='1' width='2.959' height='0.506'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
				</Paragraph>
			</Footer>
			<Body borderWidth='0.020'>
				<Paragraph>
					<TimeAxis>
						<Table rows='14' cols='2' topPadding='0.030' bottomPadding='0.010' leftPadding='0.010' rightPadding='0.010' xCfg='2'>
							<Row name='日期' height='0.604' xCfg='102'>
								<XAxis max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>日期<Font size='0.452' />
									</Paragraph>
								</Cell>
							</Row>
							<Row id='ruyuan' name='住院天数' height='0.604' xCfg='102'>
								<XAxis cfg='4802' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />住院天数</Paragraph>
								</Cell>
							</Row>
							<Row id='shoushu' name='手术后天数' height='0.604' xCfg='102'>
								<XAxis cfg='4002' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />手术后天数</Paragraph>
								</Cell>
							</Row>
							<Row name='时间' height='0.604' xCfg='102'>
								<XAxis cfg='1' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />时间</Paragraph>
								</Cell>
							</Row>
							<Row height='14.895' xCfg='202'>
								<Cell id='maibo' name='脉搏' xCfg='21' width='1.097' borderWidth='0.020' borderStyle='1' topPadding='0.200' bottomPadding='0.200'>
									<YAxisCell cfg='82' topPadding='1.800' bottomPadding='1.800' min='40.000' max='180.000' postText='次/分' size='0.370' backColor='ffffff' symbolSize='0.420' symbolColor='ff0000'>
										<YScale value='180.000' text='180' />
										<YScale value='160.000' text='160' />
										<YScale value='140.000' text='140' />
										<YScale value='120.000' text='120' />
										<YScale value='100.000' text='100' />
										<YScale value='80.000' text='80' />
										<YScale value='60.000' text='60' />
										<YScale value='40.000' text='40' />
									</YAxisCell>
									<Paragraph xCfg='2'>
										<Font size='0.360' />脉搏</Paragraph>
									<Paragraph>
										<Font fontName='ThinkEditor' color='ff0000' />M<Font color='0' />
									</Paragraph>
								</Cell>
								<Cell id='tiwen' name='体温' xCfg='21' width='1.097' borderWidth='0.020' borderStyle='1' topPadding='0.200' bottomPadding='0.200'>
									<YAxisCell cfg='1188' topPadding='1.800' bottomPadding='1.800' min='35.000' max='42.000' postText='摄氏' fontName='宋体' size='0.370' color='ff0000' backColor='ffffff' symbolSize='0.420' symbolColor='ff'>
										<YScale value='42.000' text='42' />
										<YScale value='41.000' text='41' />
										<YScale value='40.000' text='40' />
										<YScale value='39.000' text='39' />
										<YScale value='38.000' text='38' />
										<YScale value='37.000' text='37' />
										<YScale value='36.000' text='36' />
										<YScale value='35.000' text='35' />
									</YAxisCell>
									<Paragraph xCfg='2'>
										<Font size='0.360' />体温</Paragraph>
									<Paragraph>
										<Font fontName='ThinkEditor' color='ff' />K<Font color='0' />
									</Paragraph>
								</Cell>
							</Row>
							<Row height='3.514' xCfg='202'>
								<Cell name='疼痛评分' xCfg='21' eCfg='1' width='1.097' borderWidth='0.020' borderStyle='1' topPadding='0.200' bottomPadding='0.200'>
									<YAxisCell topPadding='1.970' max='100.000' size='0.370' color='ff' symbolSize='0.370' symbolColor='ff' />
									<Paragraph xCfg='2'>
										<Font size='0.390' />疼<LF />痛<LF />评<LF />分</Paragraph>
								</Cell>
								<Cell id='tengtong' xCfg='21' width='1.097' borderWidth='0.020' borderStyle='1' topPadding='0.200' bottomPadding='0.200'>
									<YAxisCell cfg='1286' topPadding='0.500' bottomPadding='0.500' max='10.000' size='0.370' backColor='ffffff' symbolSize='0.420' symbolColor='ff'>
										<YScale value='10.000' text='10' />
										<YScale value='8.000' text='8' />
										<YScale value='6.000' text='6' />
										<YScale value='4.000' text='4' />
										<YScale value='2.000' text='2' />
										<YScale text='0' />
									</YAxisCell>
								</Cell>
							</Row>
							<Row id='huxi' name='呼吸  次/分' height='0.999' xCfg='102'>
								<XAxis cfg='1014' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />呼吸<Space />次/分</Paragraph>
								</Cell>
							</Row>
							<Row id='xueya' height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />血压(mmHg)</Paragraph>
								</Cell>
							</Row>
							<Row id='ruliang' height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />入量(ml)</Paragraph>
								</Cell>
							</Row>
							<Row id='chuliang' height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.390' />出量(ml)</Paragraph>
								</Cell>
							</Row>
							<Row id='dabian' height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>大便(次/日)</Paragraph>
								</Cell>
							</Row>
							<Row id='tizhong' height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.463' />体重(kg)</Paragraph>
								</Cell>
							</Row>
							<Row id='shengao' height='0.604' xCfg='102'>
								<XAxis cfg='4' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell xCfg='10' eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph xCfg='2'>
										<Font size='0.463' />身高(cm)</Paragraph>
								</Cell>
							</Row>
							<Row height='0.604' xCfg='102'>
								<XAxis cfg='3' max='100.000' exceedColor='ff0000' size='0.320' />
								<Cell eCfg='1' width='2.195' borderWidth='0.020' borderStyle='1' topPadding='0.030' colSpan='2'>
									<Paragraph>
										<Font size='0.463' />
									</Paragraph>
								</Cell>
							</Row>
						</Table>
						<TimeGrids>
							<TimeGrid cfg='2' topPadding='1.800' bottomPadding='1.800' unitWidth='0.400' startTime='2021-09-09 00:00:00' endTime='2021-09-19 00:00:00' splitDateColor='ff0000' mainLineSpace='1.620' subLineCount='4'>
								<XScale text='2' blockValue='4.000' color='ff0000' unitRatio='1.000' />
								<XScale text='6' blockValue='4.000' color='ff0000' unitRatio='1.000' />
								<XScale text='10' blockValue='4.000' unitRatio='1.000' />
								<XScale text='14' blockValue='4.000' unitRatio='1.000' />
								<XScale text='18' blockValue='4.000' unitRatio='1.000' />
								<XScale text='22' blockValue='4.000' color='ff0000' unitRatio='1.000' />
							</TimeGrid>
						</TimeGrids>
						<TimeDatas>
							<TimeData id='xueya' name='血压'>
								<TimePoint time='2021-09-10 14:11:36' text='108/70' size='0.423' />
							</TimeData>
							<TimeData id='shengao' name='身高'>
								<TimePoint time='2021-09-10 14:11:36' value='180' />
							</TimeData>
							<TimeData id='tiwen' name='体温'>
								<TimePoint time='2021-09-10 14:11:36' value='34.5' />
								<TimePoint time='2021-09-10 17:11:36' value='36.5' />
								<TimePoint time='2021-09-10 19:11:36' value='39' />
								<TimePoint time='2021-09-11 09:11:36' value='34' lanternValue='36' />
								<TimePoint time='2021-09-11 12:11:36' value='39.3' />
								<TimePoint time='2021-09-11 16:11:36' value='34' />
								<TimePoint time='2021-09-11 21:11:36' value='36' />
								<TimePoint time='2021-09-12 09:11:36' value='39.2' lanternValue='37.5' />
								<TimePoint time='2021-09-12 13:11:36' value='36.3' />
								<TimePoint time='2021-09-12 17:11:36' value='39.3' />
								<TimePoint time='2021-09-12 21:11:36' value='34' />
								<TimePoint time='2021-09-13 01:11:36' value='36' />
								<TimePoint time='2021-09-13 05:11:36' value='39.2' />
								<TimePoint time='2021-09-13 09:11:36' value='36.3' />
								<TimePoint time='2021-09-13 13:11:36' value='36' />
								<TimePoint time='2021-09-13 17:11:36' value='39.2' />
								<TimePoint time='2021-09-13 21:11:36' value='36.3' />
							</TimeData>
							<TimeData id='xinlv' name='心率'>
								<TimePoint time='2021-09-09 10:35:06' value='100' />
								<TimePoint time='2021-09-09 14:35:06' value='90' />
								<TimePoint time='2021-09-09 18:35:06' value='80' />
								<TimePoint time='2021-09-09 22:35:06' value='60' />
								<TimePoint time='2021-09-10 02:35:06' value='70' />
								<TimePoint time='2021-09-10 06:35:06' value='110' />
								<TimePoint time='2021-09-10 10:35:06' value='120' />
								<TimePoint time='2021-09-10 14:35:06' value='100' />
							</TimeData>
							<TimeData id='maibo' name='脉搏'>
								<TimePoint time='2021-09-09 10:35:06' value='90' />
								<TimePoint time='2021-09-09 14:35:06' value='80' />
								<TimePoint time='2021-09-09 18:35:06' value='90' />
								<TimePoint time='2021-09-09 22:35:06' value='65' />
								<TimePoint time='2021-09-10 02:35:06' value='75' />
								<TimePoint time='2021-09-10 06:35:06' value='100' />
								<TimePoint time='2021-09-10 10:35:06' value='110' />
								<TimePoint time='2021-09-10 14:35:06' value='95' />
							</TimeData>
							<TimeData id='huxi' name='呼吸'>
								<TimePoint time='2021-09-09 10:35:06' value='18' />
								<TimePoint time='2021-09-09 14:35:06' value='20' />
								<TimePoint time='2021-09-09 18:35:06' value='18' />
								<TimePoint time='2021-09-09 22:35:06' value='20' />
								<TimePoint time='2021-09-10 02:35:06' value='22' />
								<TimePoint time='2021-09-10 06:35:06' value='24' />
								<TimePoint time='2021-09-10 10:35:06' value='26' />
								<TimePoint time='2021-09-10 14:35:06' value='22' />
							</TimeData>
							<TimeData id='guomin' name='药物过敏'>
								<TimePoint time='2021-09-09 10:35:06' text='青霉素' />
								<TimePoint time='2021-09-09 14:35:06' value='20' />
								<TimePoint time='2021-09-09 18:35:06' value='18' />
								<TimePoint time='2021-09-09 22:35:06' value='20' />
								<TimePoint time='2021-09-10 02:35:06' text='碘' />
								<TimePoint time='2021-09-10 06:35:06' value='24' />
								<TimePoint time='2021-09-10 10:35:06' value='26' />
								<TimePoint time='2021-09-10 14:35:06' value='22' />
							</TimeData>
							<TimeData id='tengtong' name='疼痛'>
								<TimePoint time='2021-09-09 10:35:06' value='2' />
								<TimePoint time='2021-09-09 14:35:06' value='3' />
								<TimePoint time='2021-09-09 18:35:06' value='7' />
								<TimePoint time='2021-09-09 22:35:06' value='5' />
								<TimePoint time='2021-09-10 02:35:06' value='6' />
								<TimePoint time='2021-09-10 06:35:06' value='7' />
								<TimePoint time='2021-09-10 10:35:06' value='4' />
								<TimePoint time='2021-09-10 14:35:06' value='3' />
							</TimeData>
							<TimeData id='ruyuan' name='入院'>
								<TimePoint time='2021-09-11 09:11:36' value='0' text='入院' />
							</TimeData>
							<TimeData id='shoushu' name='手术'>
								<TimePoint time='2021-09-11 16:11:36' value='0' text='手术八时十分' />
								<TimePoint time='2021-09-12 09:11:36' value='' text='转入普通病房' />
							</TimeData>
						</TimeDatas>
					</TimeAxis>
					<Font size='0.317' />
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors>
			<Editor optId='wpKT6Z000' id='1087' name='蔡芾' level='3' terminal='web' time='1675180800' />
		</Editors>
		<OptionRes>
			<Options class='系统'>
				<Option id='性别'>
					<Item id='男性' group='1' />
					<Item id='女性' group='1' />
					<Item id='未说明的性别' group='1' />
					<Item id='未知的性别' group='1' />
				</Option>
			</Options>
		</OptionRes>
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
