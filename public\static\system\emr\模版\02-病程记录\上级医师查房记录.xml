<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='2.414' bottomPadding='2.414' leftPadding='1.962' rightPadding='1.962'>
			<Header>
				<Paragraph xCfg='12' lineSpaceValue='1.500'>
					<Font size='0.635' color='d0d0d'  cfg='1' />XXX医院</Paragraph>
				<Paragraph xCfg='10'>
					<Font size='0.423' color='201f35' cfg='0' />
					<Space />
					<Element name='name' cfg='30000' hint='姓名' beforeTag='姓名:' width='2.502'>
						<BeforeTag cfg='4'>
							<Font color='ff' />姓名:</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='sickarea' cfg='30000' hint='病区' beforeTag='病区：' width='3.002'>
						<BeforeTag cfg='4'>
							<Font color='ff' />病区：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />病区</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='insickroom' cfg='30000' hint='床号' beforeTag='床号：' width='1.501'>
						<BeforeTag cfg='4'>
							<Font color='ff' />床号：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />床号</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='Regno' cfg='30000' hint='住院号' beforeTag='住院号：' width='3.002'>
						<BeforeTag cfg='4'>
							<Font color='ff' />住院号：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />住院号</Hint>
					</Element>
					<Font color='201f35' />
					<Space count='2' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='12' lineSpaceValue='1.500'>
					<Font color='201f35'  />第<PageNum width='0.851' height='0.506' lCfg='2'>
						<Unit width='0.851' height='0.506'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font color='201f35'  />页</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font color='201f35'  />
					<Element name='CourseTime' cfg='30000' inputMode='3' timeType='1' hint='病程记录时间'>
						<Hint cfg='1000'>
							<Font color='808080' />病程记录时间</Hint>
					</Element>
					<Font color='201f35' />
				</Paragraph>
				<Paragraph xCfg='110' specificIndentValue='2.000'>XX主任医师及X<SelectionBegin side='2' />
					<SelectionEnd side='2' />X主治医师查房：患者无诉不适，食欲食量尚可，查体：神志清楚，皮肤巩膜无黄染，心肺听诊无异常。腹平软，全腹无压痛及反跳痛，肝脾肋下未触及。陈主任示目前诊断：慢性HBV携带者：患者1年前发现HBsAg、HBeAg、HBcAb均阳性，HBVDNA阳性，无自觉不适，多次查肝功能均正常，故考虑此诊断。患者发现HBsAg阳性1年，应注意有无存在慢性肝炎可能，有待肝穿明确。但患者今日拒绝行彩超引导下肝穿术。故办理出院。出院诊断：慢性HBV携带者。嘱其注意休息，清淡饮食。门诊继续治疗，定期检查肝功、血常规、彩超、乙肝两对半、HBVDNA等。</Paragraph>
				<Paragraph xCfg='10' specificIndentValue='0.000'>
					<Space count='71' />
					<Element name='LoginDoct' cfg='30000' hint='登录医生'>
						<Hint cfg='1000'>
							<Font color='808080' />登录医生</Hint>
					</Element>
					<Font color='201f35' />
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
