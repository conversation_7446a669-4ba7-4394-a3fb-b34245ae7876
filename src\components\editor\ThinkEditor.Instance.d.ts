import { ThinkEditor } from "./ThinkEditor";
import { ThinkEditorEvent } from "./ThinkEditor.Defined";
export declare class ThinkEditorInstance extends EventTarget {
    instanceId?: string;
    editors: Array<ThinkEditor>;
    constructor(instanceId?: string);
    AddEditor(thinkEditor: ThinkEditor): boolean;
    private SelectEditorByEditorId;
    SelectEditor(docName: string): boolean;
    CreateInstanceChangeEvent(): void;
    private CreateEditorFocusEvent;
    dispatchEditorsInstanceEvent(e: ThinkEditorEvent): boolean;
    GetLastEditor(): ThinkEditor | undefined;
    CloseEditor(docName: string): boolean;
    CloseAllEditors(): void;
    private CloseDocEditorHandle;
    GetEditorByShowName(showName: string): ThinkEditor | undefined;
    GetEditor(docName: string): ThinkEditor | undefined;
    GetEditorByEditorId(editorId?: string): ThinkEditor | undefined;
    GetSelectedEditor(): ThinkEditor | undefined;
    GetSelectedDocName(): string;
    GetEditorCount(): number;
    private addInnerEventListenerAgent;
    private removeInnerEventListenerAgent;
    private OnInnerEvent;
}
//# sourceMappingURL=ThinkEditor.Instance.d.ts.map