export declare function GetRuntimeVersion(): string;
export declare class ClipboardDto {
    xml?: string;
    textEx?: string;
    text?: string;
    html?: string;
    image?: any;
    constructor(xmlStr?: string, textExStr?: string, textStr?: string, htmlStr?: string, image?: any);
}
export interface IClipboard {
    Write(e: ClipboardEvent, dto: ClipboardDto): void;
    ReadAsync(): Promise<ClipboardDto | undefined>;
    ReadEventAsync(e: ClipboardEvent): Promise<ClipboardDto | undefined>;
    textFormat: string;
    textExFormat: string;
    xmlFormat: string;
    htmlFormat: string;
    clipboardType: string;
}
export declare class ClipboardNormal implements IClipboard {
    textFormat: string;
    textExFormat: string;
    xmlFormat: string;
    htmlFormat: string;
    clipboardType: string;
    constructor();
    Write(e: ClipboardEvent, dto: ClipboardDto): void;
    ReadAsync(): Promise<ClipboardDto | undefined>;
    ReadEventAsync(e: ClipboardEvent): Promise<ClipboardDto | undefined>;
}
export declare class ClipboardAdvanced implements IClipboard {
    textFormat: string;
    textExFormat: string;
    xmlFormat: string;
    htmlFormat: string;
    clipboardType: string;
    constructor();
    Write(e: ClipboardEvent, dto: ClipboardDto): void;
    ReadAsync(): Promise<ClipboardDto | undefined>;
    ReadEventAsync(e: ClipboardEvent): Promise<ClipboardDto | undefined>;
}
export declare class ClipboardElectron implements IClipboard {
    textFormat: string;
    textExFormat: string;
    xmlFormat: string;
    htmlFormat: string;
    clipboardType: string;
    constructor();
    Write(e: ClipboardEvent, dto: ClipboardDto): void;
    ReadAsync(): Promise<ClipboardDto | undefined>;
    ReadEventAsync(e: ClipboardEvent): Promise<ClipboardDto | undefined>;
}
export declare class WordGraph {
    font: string;
    word: string;
    wordId?: number;
    minx?: number;
    maxx?: number;
    miny?: number;
    maxy?: number;
    ascent?: number;
    descent?: number;
    advance?: number;
    width: number;
    height: number;
    pixSize: number;
    wordViewWidth: number;
    wordViewHeight: number;
    expWidth: number;
    textMetrics?: TextMetrics;
    private surfaces;
    private graphInfo?;
    constructor(word: string, wordId: number);
    BindTextMerics(textMetrics: TextMetrics): void;
    CalcWordLayoutHeight(): number;
    CalcWordViewHeight(): number;
    CalcWordGraphAscent(): number;
    CalcWordGraphDescent(): number;
    GetWordGraphInfo(): string;
    parseFontColor(fontColor: number): string;
    GetWordSurface(ctx: OffscreenCanvasRenderingContext2D, fontColor: number): ImageData | undefined;
}
export declare class WordGraphManager {
    private wordGraphCanvas;
    private ctx?;
    private wordsGraphs;
    private fontNameMap;
    constructor();
    GetSwapFontName(fontName: string): string;
    parseFontStyle(fontName: string, wordGraphId: number): string;
    GetWordSurface(fontName: string, fontColor: number, word: string, wordId: number): ImageData | undefined;
    GetWordGraph(fontName: string, word: string, wordId: number): WordGraph;
}
export declare function FlushFrameData(thinkEditor: any): void;
export declare function CreateInstance(lib: string | ArrayBuffer): Promise<any>;
//# sourceMappingURL=ThinkEditor.Runtime.d.ts.map