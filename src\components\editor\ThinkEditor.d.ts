import { <PERSON><PERSON>anager, E_MESSAGE_LEVEL, E_BARCODE_TYPE, E_VIEW_MODE, E_DOC_FORMAT, E_TABLE_INSERT_MODE, E_TABLE_DELETE_MODE, E_CELL_OPT_MODE, E_ALIGN_HORIZONTAL_MODE, E_ALIGN_VERTICAL_MODE, ElementCfg, E_TIME_TYPE, QCCfg, E_DOC_TYPE, E_IMAGE_TYPE, E_IDENTITY_OBJECT_TYPE, E_DISPLAY_MODE, E_BRUSH_STATE, E_PASTE_TYPE, E_SCRIPT_MODE, E_FERRULE_MODE, E_LIST_MODE, E_LIST_LEVEL, E_SPECIFIC_INDENT_FORMAT, E_SET_MODE, Source } from "./ThinkEditor.Defined";
export declare class Timestamp {
    createTime: Date;
    beginInitTime?: Date;
    endInitTime?: Date;
    beginDownloadFontTime?: Date;
    endDownloadFontTime?: Date;
    beginParseTime?: Date;
    endParseTime?: Date;
    beginRequestFontTime?: Date;
    endRequestFontTime?: Date;
    beginParseFontTime?: Date;
    endParseFontTime?: Date;
    beginProduceLinesTime?: Date;
    endProduceLinesTime?: Date;
    beginProducePagesTime?: Date;
    endProducePagesTime?: Date;
    fristDrawTime?: Date;
    GetTimeLog(): string;
}
export declare class ThinkEditor extends EventTarget {
    docName: string;
    showName: string;
    selected: boolean;
    selectedTime: Date;
    isInputFocus?: boolean;
    container?: HTMLDivElement;
    isLoaded: boolean;
    private forbidCallAutoSetInputFocus?;
    private clipboardProcessor;
    private canvas?;
    private input?;
    private context?;
    timestamp: Timestamp;
    sourcesManager: SourcesManager;
    private readonly _param;
    private instance;
    private errorCount;
    private inputMethodText;
    private lastInputTime;
    _OnDocumentKeyDown: (e: KeyboardEvent) => Promise<void>;
    _OnDocumentKeyUp: (e: KeyboardEvent) => void;
    _OnDocumentMouseDown: (e: MouseEvent) => void;
    _OnDocumentMouseUp: (e: MouseEvent) => void;
    _OnDocumentMouseMove: (e: MouseEvent) => void;
    _OnDocumentPaste: (e: ClipboardEvent) => Promise<void>;
    _OnDocumentCopy: (e: ClipboardEvent) => void;
    _OnDocumentCut: (e: ClipboardEvent) => void;
    _OnDocumentClick: (e: MouseEvent) => void;
    constructor(param: object);
    get param(): object;
    get lib(): any;
    get fontPath(): any;
    get editorId(): any;
    InitAsync(): Promise<boolean>;
    private InitInternal;
    private PrintVersion;
    Init(): Promise<boolean>;
    UnInit(): void;
    private UnInitCanvas;
    private UnInitInput;
    Load(container?: HTMLDivElement): void;
    UnLoad(): void;
    private InitCanvas;
    private InitCanvasOuterEventListener;
    private UnInitCanvasOuterEventListener;
    private InitDocumentOuterEventListener;
    private UnInitDocumentOuterEventListener;
    private InitInput;
    private InitEditorOuterEventListener;
    private UnInitEditorOuterEventListener;
    private GetFrameData;
    private GetCurrentEditorSize;
    private InitEditorInnerEventListener;
    private InitInner;
    private OnDispatchOuterEvent;
    private OnInputFocus;
    private OnInputBlur;
    private OnInputCompositionStart;
    private OnInputCompositionEnd;
    private OnInputInput;
    private OnInputKeyDown;
    private OnInputKeyUp;
    private OnCanvasTouchStart;
    private OnCanvasMouseMove;
    private OnCanvasMouseOut;
    private OnCanvasMouseOver;
    private OnCanvasMouseClick;
    private OnCanvasMouseDown;
    private OnCanvasMouseUp;
    private OnCanvasMouseWheel;
    private OnCanvasDragoverEvent;
    private OnCanvasDropEvent;
    private OnDocumentKeyDown;
    private OnDocumentKeyUp;
    private OnDocumentMouseDown;
    private OnDocumentMouseUp;
    private OnDocumentClick;
    private getElementPos;
    private OnDocumentMouseMove;
    private OnDocumentPaste;
    private OnDocumentCopy;
    private OnDocumentCut;
    private OnSetCursorStyle;
    private OnTextEditFocused;
    private CreateTimeStampEvent;
    private OnTimeStamp;
    private OnSetOptionsEvent;
    private OnSetOptionsRequest;
    private OnSetOptions;
    private OnDelOptions;
    private OnSetSourcesEvent;
    private OnSetSourcesRequest;
    private OnSetSources;
    private OnSetSearchHistoryEvent;
    private OnSetSearchHistoryRequest;
    private OnSetSearchHistory;
    private OnSetReplaceHistoryEvent;
    private OnSetReplaceHistoryRequest;
    private OnSetReplaceHistory;
    private OnSetSpecialSymbolHistoryHistoryEvent;
    private OnSetSpecialSymbolHistoryHistoryRequest;
    private OnSetSpecialSymbolHistory;
    private OnFontsName;
    private OnSetAlgorithmsEvent;
    private OnSetAlgorithmsRequest;
    private OnSetAlgorithms;
    private OnExecuteElementScript;
    private OnExecuteCheckBoxScript;
    private OnPrintDoc;
    private OnRequestFont;
    private LoadRequestFontProcessSpeedMode;
    private LoadRequestFontProcessStrictMode;
    private LoadRequestFontProcess;
    private GetFontData;
    addEventListener(type: string, callback: EventListenerOrEventListenerObject | null, options?: AddEventListenerOptions | boolean): void;
    private NeedSetInputFocus;
    private Call;
    GetViewFrameData(): Uint8Array;
    private CanCopyToOuter;
    private CanPasteFromOuter;
    SetFontConfig(jsonObj?: object): boolean;
    SetInputFocus(state: boolean): void;
    SetEditorFocus(): void;
    SetDocConfig(docName: string, jsonObj: object): any;
    IsEditorFocus(): boolean | undefined;
    GetSelectRangeContent(docFormat: E_DOC_FORMAT, jsonCfg?: object): string;
    InsertImage(id: string, width: number, height: number, filename: string, arrayBuffer: ArrayBuffer, data_len: number, jsonObj: any): string;
    private InputFragmentPaste;
    LoadFont(fontName: string, arrayBuffer: ArrayBuffer): boolean;
    LoadRequestFont(fontName: string, arrayBuffer: ArrayBuffer, candidateFontInfo: object): boolean;
    SetFontInfo(fontInfos: object, cfg: object): void;
    GetFontInfo(): any;
    ResizeViewPanel(width: number, heigth: number): boolean;
    SetEditorConfig(object: Object): any;
    InputText(text: string): boolean;
    private InputMethod;
    SetSystemSource(sourceClass: string, sourceId: string, fragment: string): any;
    SetSource(sourceClass: string, sourceId: string, fragment: string): boolean;
    SetSources(sources: Array<Source>, jsonCfg?: object): boolean;
    SetDataSources(obj: object | undefined, param: object): boolean;
    AddSystemKnowledge(knowledge: string | ArrayBuffer): any;
    AddDocKnowledge(knowledge: string | ArrayBuffer): any;
    SetOptions(options: object): any;
    DelOptions(options: object): any;
    SetAlgorithms(algorithms: object): any;
    DelAlgorithms(algorithms: object): any;
    SetDefaultFont(fontName: string, fontSize: number): any;
    SetViewMode(viewMode: E_VIEW_MODE, cfg?: object): void;
    CreateDoc(docName: string): any;
    ParseDoc(docName: string, xml: string | ArrayBuffer, jsonCfg: object): boolean;
    ParseDocs(docName: string, subDocName: string, subDocXml: string | ArrayBuffer, jsonCfg: object): boolean;
    SelectDoc(docName: string, jsonCfg?: object): any;
    SelectAll(): any;
    PushSelection(cnt?: number): any;
    PopSelection(): any;
    CloseDoc(docName: string): void;
    CloseAllDoc(): void;
    SetUserProfile(jsonCfg: string): boolean;
    SetEditorInfo(terminal: string, editorId: string, editorName: string, permissionLevel: number, jsonAttrs: any): boolean;
    UpdateEditorInfo(optId: string, terminal: string, editorId: string, editorName: string, permissionLevel: number, editorTime: string, jsonAttrs: any): boolean;
    SetDocType(docType: number): boolean;
    SetEditMode(enablePermission: number, enableRevise: number, modifySameLevel: number, fristEditByRevise?: number, modifyOwnerOldByRevise?: number): boolean;
    SetDocClass(docClass: string): boolean;
    SetDisplayScale(pagesLayoutMode: number, value: number): void;
    InsertSeparator(width: number, height: number, jsonCfg: object): boolean;
    InsertPageFeed(count: number): any;
    InsertLineFeed(count: number): any;
    InsertFormula(jsonCfg: object): boolean;
    SetFormulaConfig(id: string, jsonCfg: object): boolean;
    InsertBarCode(jsonCfg: object): boolean;
    SetBarCodeConfig(id: string, jsonCfg: object): boolean;
    GetBarCodeClass(barcodeType: E_BARCODE_TYPE): number;
    SetBarCodeIdentity(id: string, newId: string, newName: string): boolean;
    GetBarCodeList(): object;
    CalcBarCodeProperties(jsonCfg: object): object;
    InsertAnnotate(id: string, fragment: string, jsonCfg: object): any;
    InsertButton(jsonCfg: object): any;
    InsertComment(id: string, fragment: string, jsonCfg: object): any;
    DeleteComment(jsonCfg: object): boolean;
    InsertReplyComment(fragment: string, jsonCfg: object): any;
    SetCommentResolve(jsonCfg: object): boolean;
    DeleteReplyComment(jsonCfg: object): boolean;
    SelectComments(jsonCfg: object): any;
    SelectComment(jsonCfg: object): any;
    GetCommentReport(): any;
    SelectQualityControl(jsonCfg: object): any;
    InsertElement(jsonCfg: object): boolean;
    DeleteElement(id: string, jsonCfg: object): boolean;
    InsertCheckBox(jsonCfg: object): boolean;
    InsertCheckBoxGroup(jsonCfg: object): boolean;
    SetCheckBoxConfig(id: string, jsonCfg: object): boolean;
    GetCheckBoxProperties(jsonCfg?: object): boolean;
    InsertPageNum(jsonCfg: object): boolean;
    SetPageNumConfig(id: string, jsonCfg: object): boolean;
    GetTableProperties(tableId: string, jsonCfg: object): any;
    InsertTable(jsonCfg: object): boolean;
    SelectTable(id: string, jsonCfg: object): boolean;
    TableInsertOpt(insertMode: E_TABLE_INSERT_MODE): void;
    TableDeleteOpt(deleteMode: E_TABLE_DELETE_MODE): void;
    CellOpt(mode: E_CELL_OPT_MODE): void;
    SetTableConfig(id: string, jsonCfg: object): boolean;
    SetTableData(id: string, tableXml: string | ArrayBuffer, jsonCfg: object): boolean;
    SetCellContent(param: object): boolean;
    SetCellContentCombine(param: object): boolean;
    SetTableAlign(mode: E_ALIGN_HORIZONTAL_MODE): void;
    SetCellAlignContent(mode: E_ALIGN_VERTICAL_MODE): void;
    InsertParagraph(jsonCfg: object): boolean;
    DeleteParagraph(jsonCfg: object): boolean;
    SetParagraphConfig(id: string, jsonCfg: object): boolean;
    SetElementBase(id: string, hint: string, tip: string, startBorder: string, endBorder: string, beforeTag: string, afterTag: string): boolean;
    SetElementIdentity(id: string, newId: string, newName: string): boolean;
    SetElementAttributes(id: string, attrs: object): boolean;
    AddElementAttribute(id: string, key: string, value: string): boolean;
    SetElementSelectItems(id: string, itemsStr: string): boolean;
    SetElementContent(id: string, content: string, jsonCfg?: object): boolean;
    SetElementsContent(keyValues: object, jsonCfg?: object): boolean;
    SetElementConfig(id: string, jsonCfg: object): boolean;
    SetElementsConfig(identityArr: object, jsonCfg?: object): boolean;
    GetElementProperties(jsonCfg?: object): boolean;
    GetElementsContent(ids?: object, jsonCfg?: object): boolean;
    SetElementInputText(id: string): boolean;
    SetElementInputNumber(id: string): boolean;
    SetElementSelectOption(id: string, optionClass: string, optionId: string, itemLinkStr: string, elementCfg: ElementCfg): any;
    SetElementSelectDateTime(id: string, dateType: E_TIME_TYPE): any;
    SetElementQCNoticeMsg(id: string, msgLevel: E_MESSAGE_LEVEL, msg: string): any;
    SetElementQCString(id: string, minLen: number, maxLen: number, qcCfg: QCCfg): any;
    SetElementQCNumber(id: string, minValue: number, maxValue: number, decimalCount: number, qcCfg: QCCfg): any;
    SetElementQCDateTime(id: string, minDatetime: string, maxDatetime: string, qcCfg: QCCfg): any;
    SetElementExcludeKeywords(id: string, keywords: string): any;
    SetElementSource(id: string, sourceClass: string, sourceId: string, elementCfg: ElementCfg): any;
    InsertImageBase64(id: string, width: number, height: number, filename: string, base64Str: string, base64StrLen: number, jsonCfg: object): boolean;
    SetImageConfig(id: string, jsonCfg: object): boolean;
    SetImageResource(imageName: string, arrayBuffer: ArrayBuffer, dataLen: number, jsonCfg: object): string;
    SetImageResourceBase64(fileName: string, base64Str: string, base64StrLen: number, jsonCfg: object): string;
    GetDoc(docFormat: E_DOC_FORMAT, docType: E_DOC_TYPE, jsonCfg: object): string;
    GetSourceList(docName: string): object;
    GetAlgorithmList(): object;
    GetOptionList(): object;
    GetSelectDocName(): string;
    GetVisibleDocName(): string;
    GetPagesImage(imageType: E_IMAGE_TYPE, startIndex: number, pagesCount: number, scale: number): any;
    GetPagesPDF(startIndex: number, pagesCount: number, scale: number, jsonCfg: object): any;
    PrintPDF(jsonCfg: object): Promise<void>;
    PrintPdfAsync(jsonCfg: object): Promise<void>;
    private GetLostFontinternal;
    GetPDF(jsonCfg: object): Promise<any>;
    GetPdfAsync(jsonCfg: object): Promise<any>;
    GetLostFontInfos(): any;
    GetObjectFragment(identityObjectType: E_IDENTITY_OBJECT_TYPE, id: string, docFormat: E_DOC_FORMAT, jsonCfg: object): any;
    PrintDoc(jsonCfg: object): Promise<void>;
    PrintDocAsync(jsonCfg: object): Promise<void>;
    InputData(data: string): any;
    InputFragment(fragment: string | ArrayBuffer, jsonCfg: object): any;
    AppendFragment(docName: string, fragment: string | ArrayBuffer, jsonCfg: object): any;
    UpdateServerTime(serverTime: string): void;
    NewDoc(docName: string, docType: E_DOC_TYPE): any;
    VisibleDoc(docName: string): any;
    SetDocAttributes(atts: object): void;
    AddDocAttribute(key: string, value: string): void;
    GetFocusProperties(jsonCfg?: object): any;
    GetDocProperties(): any;
    GetEditorProperties(): any;
    SetObjectOwner(jsonCfg: object): any;
    SetObjectClaim(jsonCfg: object): any;
    DisplayPageSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplaySpecialSymbolSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplayInputHandleSelector(displayMode: E_DISPLAY_MODE): void;
    DisplayToothPositionSelector(displayMode: E_DISPLAY_MODE): void;
    ClearFormat(): void;
    SetFormatBrush(brushState: E_BRUSH_STATE): void;
    Undo(count: number): void;
    Redo(count: number): void;
    Copy(): void;
    Cut(): void;
    Paste(pasteType: E_PASTE_TYPE): Promise<void>;
    Delete(): void;
    PasteInner(pasteType: E_PASTE_TYPE): void;
    DoPaste(xmlStr: string, textExStr: string, htmlStr: string, textStr: string, image: any, pasteType?: E_PASTE_TYPE): Promise<boolean>;
    PasteV1(pasteType: E_PASTE_TYPE | undefined): Promise<boolean>;
    PasteV2(pasteType: E_PASTE_TYPE | undefined): Promise<boolean | undefined>;
    SetFontType(fontName: string): void;
    SetFontSize(fontSize: number): void;
    SetFontSizeItem(fontItem: string): any;
    SetFontScript(scriptMode: E_SCRIPT_MODE): void;
    SetFontItalic(italic: boolean): void;
    SetFontBold(bold: boolean): void;
    SetFontUnderline(underline: boolean): void;
    SetFontStrikethrough(strikethrough: boolean): void;
    SetFontColor(color: string): void;
    SetFontBackColor(color: string): void;
    SetFontFerrule(mode: E_FERRULE_MODE): void;
    SetParagraphContent(id: string, content: string, jsonCfg: object): any;
    SetParagraphListMode(listMode: E_LIST_MODE): any;
    SetParagraphListLevel(listLevel: E_LIST_LEVEL): void;
    SetParagraphListHeaderStr(headerStr: string): any;
    SetParagraphSpecificIndentFormat(specificIndentFormat: E_SPECIFIC_INDENT_FORMAT): void;
    SetParagraphAlignContent(alignmentHorizontalMode: E_ALIGN_HORIZONTAL_MODE): void;
    SetParagraphLeftIndent(indent: number): void;
    SetParagraphRightIndent(indent: number): void;
    SetParagraphSpecificIndentCharacterCount(count: number): void;
    SetParagraphLineSpacingValue(value: number): void;
    SetParagraphLineSpacingRule(rule: number): void;
    SetParagraphSpaceBefore(space: number): void;
    SetParagraphSpaceAfter(space: number): void;
    SetWaterMarkConfig(id: string, jsonCfg?: object): any;
    GetWaterMarkReport(): any;
    GetMenu(): any;
    SetBackgroundColor(backColor: string): void;
    DisplaySeparatorSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplayElementSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE, jsonCfg?: object): void;
    DisplayCheckBoxSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayTableBoxSelector(displayMode: E_DISPLAY_MODE): void;
    DisplayPageNumSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayFormulaSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayImageSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayImageEditWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayBarCodeSetWindow(displayMode: E_DISPLAY_MODE, setMode: E_SET_MODE): void;
    DisplayMessageBoxWindow(displayMode: E_DISPLAY_MODE, jsonCfg: object): void;
    DisplayPrintSetWindow(displayMode: E_DISPLAY_MODE, jsonCfg: object): void;
    DisplayKnowledgeSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplayTableSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplaySearchReplaceWindow(displayMode: E_DISPLAY_MODE): void;
    DisplayFontSetWindow(displayMode: E_DISPLAY_MODE): void;
    DisplayParagraphSetWindow(displayMode: E_DISPLAY_MODE): void;
    SetSelectContentLock(lock: boolean, jsonCfg: object): any;
    GetOutline(jsonCfg: object): any;
    GetDocSourceReport(docName: string): any;
    GetEditorsInfo(docName: string): any;
    GetQualityControlReport(): any;
    GetReviseReport(): any;
    SelectRevise(jsonCfg: object): any;
    SetReviseConfig(jsonCfg: object): any;
    GotoOutline(jsonCfg: object): any;
    SelectAnnotates(jsonCfg: object): any;
    Search(searchCfg: object): any;
    SearchReplace(searchCfg: object): any;
    SetPageConfig(jsonCfg: object): any;
    GetPageProperties(): any;
    SetHeaderConfig(jsonCfg: object): any;
    SetFooterConfig(jsonCfg: object): any;
    GetTimeAxisConfig(id: string): any;
    SetTimeAxisConfig(id: string, jsonCfg: object): any;
    SetTimeGridConfig(jsonCfg: object): any;
    SetTimeData(jsonDataArr: object): any;
    UpdateTimeAxis(id: string): any;
    private WriteClipboard;
}
//# sourceMappingURL=ThinkEditor.d.ts.map