<template>
  <div class="fold-filed">
    <el-card>
      <el-button size="small" v-for="(item, idx) in firestTagList" @click="emitToolbarClick(item)" :key="idx">{{ item.label }}</el-button>
    </el-card>
    <el-card>
      <el-button size="small" class="single" @click="emitToolbarClick(item)" v-for="(item, idx) in twoTagList" :key="idx">{{ item.label }}</el-button>
    </el-card>
    <el-card>
      <el-button size="small" v-for="(item, idx) in threeTagList" @click="emitToolbarClick(item)" :key="idx">{{ item.label }}</el-button>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 定义emit
const emit = defineEmits(['toolbarClick']);

// 工具栏点击事件处理函数
const emitToolbarClick = (item) => {
  emit('toolbarClick', item);
};

// 第一组按钮列表
const firestTagList = ref([
  {
    label: '新建',
    val: 'newDoc'
  },
  {
    label: '保存',
    val: 'saveDoc'
  },
  {
    label: '保存为模版',
    val: 'saveAsTemplate'
  },
  //{
  //  label: '打开',
  //  val: 'openDoc'
  //},
  //{
  //  label: '查看XML',
  //  val: 'viewXML'
  //},
  {
    label: '转HTML',
    val: 'toHTML'
  },
  {
    label: '转PDF',
    val: 'toPdf'
  },
  {
    label: '转图片',
    val: 'toImg'
  }
]);

// 第二组按钮列表
const twoTagList = ref([
  {
    label: '页面设置',
    val: 'setPage'
  }
]);

// 第三组按钮列表
const threeTagList = ref([
  {
    label: '整洁编辑',
    val: 'cleanEdit'
  },
  {
    label: '审阅编辑',
    val: 'reviseEdit'
  },
  {
    label: '整洁浏览',
    val: 'cleanBrowser'
  },
  {
    label: '审阅浏览',
    val: 'reviseBrowser'
  },
  {
    label: '整洁打印',
    val: 'cleanPrint'
  },
  {
    label: '审阅打印',
    val: 'revisePrint'
  },
  {
    label: '打印选择页',
    val: 'SelectPagePrint'
  },
  {
    label: '选择打印',
    val: 'SelectContentPrint'
  },
  {
    label: '续打',
    val: 'ContinuePrint'
  }
]);
</script>

<style lang="scss">
.fold-filed {
  width: 75%;
  display: flex;
  
  .el-card {
    overflow: visible;
    margin-right: 3px;
  }
  
  .el-card__body,
  .el-main {
    padding: 2px;
  }
  
  .el-card__body .el-button:first-child {
    margin-left: 4px;
  }
  
  .el-button {
    &.single {
      margin-top: 6px;
    }
  }

  .el-button + .el-button {
    margin-left: 4px;
    margin-top: 6px;
  }
  
  // 更新为Element Plus的按钮尺寸
  .el-button--small {
    padding: 7px 12px;
  }
}
</style>