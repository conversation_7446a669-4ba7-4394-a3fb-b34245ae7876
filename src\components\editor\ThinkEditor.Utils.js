function isnull(value) {
    return !value ? true : false;
}
function notnull(value) {
    return value ? true : false;
}
function IsNullOrUndefined(value) {
    return value == null || value === undefined ? true : false;
}
function MustNotNullOrUndefined(parameterIndex, propertyKey, value) {
    if (value == null || value === undefined) {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must not be null/undefined. Received type:[${typeof value}] value:[${value}]`);
    }
}
function MustColor(parameterIndex, propertyKey, color) {
    if (IsNullOrUndefined(color)) {
        return;
    }
    if (typeof color == "number") {
        if (Number.isInteger(color)) {
            return;
        }
        else {
            throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey}  must be a color format eg. red format is number: 0x00ff00 or rgb: 'ff0000' or argb: 'ffff0000'. Received type:[${typeof color}] value:[${color}]`);
        }
    }
    if (typeof color !== "string") {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey}  must be a color format eg. red format is number: 0x00ff00 or rgb: 'ff0000' or argb: 'ffff0000'. Received type:[${typeof color}] value:[${color}]`);
    }
    if (color.length != 6 && color.length != 8) {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey}  must be a color format eg. red format is number: 0x00ff00 or rgb: 'ff0000' or argb: 'ffff0000'. Received type:[${typeof color}] value:[${color}]`);
    }
    let regExp = new RegExp("^[0-9A-Fa-f]+$");
    if (!regExp.test(color)) {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey}  must be a color format eg. red format is number: 0x00ff00 or rgb: 'ff0000' or argb: 'ffff0000'. Received type:[${typeof color}] value:[${color}]`);
    }
}
function MustObject(parameterIndex, propertyKey, value) {
    if (IsNullOrUndefined(value)) {
        return;
    }
    if (typeof value !== "object") {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be a object. Received type:[${typeof value}] value:[${value}]`);
    }
}
function MustNumRange(parameterIndex, propertyKey, value, limitStr) {
    if (IsNullOrUndefined(value)) {
        return;
    }
    let inclueMin = limitStr[0] == "[" ? true : false;
    let inclueMax = limitStr.charAt(limitStr.length - 1) == "]" ? true : false;
    let splitCharIdx = limitStr.indexOf(",");
    let minNum = Number(limitStr.substring(1, splitCharIdx));
    let maxNum = Number(limitStr.substring(splitCharIdx + 1, limitStr.length - 1));
    if (typeof value !== "number" || value < minNum || (!inclueMin && value == minNum) || value > maxNum || (!inclueMax && value == maxNum)) {
        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be a number between ${limitStr[0]}${minNum} ~ ${maxNum}${limitStr.charAt(limitStr.length - 1)}. Received type:[${typeof value}] value:[${value}]`);
    }
}
function MustInteger(parameterIndex, propertyKey, value) {
    if (IsNullOrUndefined(value)) {
        return;
    }
    if (typeof value === "boolean") {
        return true;
    }
    if (typeof value === "number" && Number.isInteger(value)) {
        return true;
    }
    throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be a Integer or Enum. Received type:[${typeof value}] value:[${value}]`);
}
function MustStr(parameterIndex, propertyKey, value) {
    if (IsNullOrUndefined(value)) {
        return;
    }
    if (typeof value === "string") {
        return true;
    }
    throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be string. Received type:[${typeof value}] value:[${value}]`);
}
function MustNotEmptyStr(parameterIndex, propertyKey, value) {
    if (IsNullOrUndefined(value)) {
        return;
    }
    if (typeof value === "string" && value.length > 0) {
        return true;
    }
    throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be not empty string. Received type:[${typeof value}] value:[${value}]`);
}
export function Sleep(seconds) {
    return new Promise((resolve) => setTimeout(resolve, seconds));
}
export function SetFontConfigCfgValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustObject(parameterIndex, propertyKey, value);
            MustColor(parameterIndex, propertyKey, value === null || value === void 0 ? void 0 : value.color);
        });
    };
}
export function InsertSeparatorCfgValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustObject(parameterIndex, propertyKey, value);
            MustNumRange(parameterIndex, propertyKey, value === null || value === void 0 ? void 0 : value.lineWidth, "[0, 0.8]");
            MustColor(parameterIndex, propertyKey, value === null || value === void 0 ? void 0 : value.color);
        });
    };
}
export function ColorValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustNotNullOrUndefined(parameterIndex, propertyKey, value);
            MustColor(parameterIndex, propertyKey, value);
        });
    };
}
export function NotEmptyStrValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustNotNullOrUndefined(parameterIndex, propertyKey, value);
            MustNotEmptyStr(parameterIndex, propertyKey, value);
        });
    };
}
export function StrValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustNotNullOrUndefined(parameterIndex, propertyKey, value);
            MustStr(parameterIndex, propertyKey, value);
        });
    };
}
export function NumValidator(limitStr) {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustNotNullOrUndefined(parameterIndex, propertyKey, value);
            MustNumRange(parameterIndex, propertyKey, value, limitStr);
        });
    };
}
export function IntValidator() {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            MustNotNullOrUndefined(parameterIndex, propertyKey, value);
            MustInteger(parameterIndex, propertyKey, value);
        });
    };
}
export function Format(fmt) {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            if (typeof value !== "string" || !fmt.test(value)) {
                throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must comply with the ${fmt} rule. Received: ${value}`);
            }
        });
    };
}
export function Fields(fields) {
    return function (target, propertyKey, parameterIndex) {
        const key = `__validator__${String(propertyKey)}`;
        if (!target[key]) {
            target[key] = [];
        }
        target[key].push((args) => {
            const value = args[parameterIndex];
            if (typeof value == "object") {
                const keys = Object.keys(value);
                for (const field of fields) {
                    if (keys.indexOf(field) == -1) {
                        throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must include ${field} field.`);
                    }
                }
            }
            else {
                throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} must be an object.`);
            }
        });
    };
}
export function NotNull(target, propertyKey, parameterIndex) {
    const key = `__validator__${String(propertyKey)}`;
    if (!target[key]) {
        target[key] = [];
    }
    target[key].push((args) => {
        const value = args[parameterIndex];
        if (!value) {
            throw new Error(`Parameter at index ${parameterIndex} in method ${propertyKey} is null or undfined.`);
        }
    });
}
export function Validator(target, propertyKey, descriptor) {
    const method = descriptor.value;
    descriptor.value = function (...args) {
        const key = `__validator__${String(propertyKey)}`;
        const validators = target[key] || [];
        for (const validator of validators) {
            validator(args);
        }
        return method.apply(this, args);
    };
}
export function IsHavingAdvancedPermissions() {
    if ("https:" == document.location.protocol || window.location.hostname === "localhost") {
        return true;
    }
    return false;
}
export function IsElectron() {
    if (navigator.userAgent.includes("Electron")) {
        return true;
    }
    else {
        return false;
    }
}
export function GetSysPlatform() {
    const userAgent = navigator.userAgent;
    if (/Windows/.test(userAgent)) {
        return "Windows";
    }
    else if (/Linux/.test(userAgent)) {
        return "Linux";
    }
    else if (/Mac/.test(userAgent)) {
        return "Mac";
    }
    else {
        return "Unknown";
    }
}
export function IsASCII(word) {
    if (word.length == 0) {
        return false;
    }
    const code = word.charCodeAt(0);
    return code >= 0 && code <= 127;
}
export function getOsInfo() {
    var userAgent = navigator.userAgent.toLowerCase();
    var name = "Unknown";
    var version = "Unknown";
    if (userAgent.indexOf("win") > -1) {
        name = "Windows";
        if (userAgent.indexOf("windows nt 5.0") > -1) {
            version = "2000";
        }
        else if (userAgent.indexOf("windows nt 5.1") > -1 || userAgent.indexOf("windows nt 5.2") > -1) {
            version = "XP";
        }
        else if (userAgent.indexOf("windows nt 6.0") > -1) {
            version = "Vista";
        }
        else if (userAgent.indexOf("windows nt 6.1") > -1 || userAgent.indexOf("windows 7") > -1) {
            version = "7";
        }
        else if (userAgent.indexOf("windows nt 6.2") > -1 || userAgent.indexOf("windows 8") > -1) {
            version = "8";
        }
        else if (userAgent.indexOf("windows nt 6.3") > -1) {
            version = "8.1";
        }
        else if (userAgent.indexOf("windows nt 6.2") > -1 || userAgent.indexOf("windows nt 10.0") > -1) {
            version = "10";
        }
        else {
            version = "Unknown";
        }
    }
    else if (userAgent.indexOf("iphone") > -1) {
        name = "Iphone";
    }
    else if (userAgent.indexOf("mac") > -1) {
        name = "Mac";
    }
    else if (userAgent.indexOf("x11") > -1 || userAgent.indexOf("unix") > -1 || userAgent.indexOf("sunname") > -1 || userAgent.indexOf("bsd") > -1) {
        name = "Unix";
    }
    else if (userAgent.indexOf("linux") > -1) {
        if (userAgent.indexOf("android") > -1) {
            name = "Android";
        }
        else {
            name = "Linux";
        }
    }
    else {
        name = "Unknown";
    }
    return name + " " + version;
}
export function getBrowerInfo() {
    var ua = navigator.userAgent;
    var temp, name = "unknown", version = "unknown";
    var browserMatch = ua.match(/(opera|chrome|safari|firefox|msie|trident|edge)\/?\s*(\.?\d+(\.\d+)*)/i);
    if (browserMatch && browserMatch[1] && browserMatch[2]) {
        name = browserMatch[1].toLowerCase();
        version = browserMatch[2];
        if (name === "trident") {
            var rv = ua.indexOf("rv:") > -1 ? parseInt(ua.substring(ua.indexOf("rv:") + 3, ua.indexOf(")", ua.indexOf("rv:"))), 10) : false;
            if (rv) {
                name = "ie";
                version = rv.toString();
            }
        }
    }
    if (name === "unknown") {
        if (ua.indexOf("QQBrowser") > -1) {
            name = "qqbrowser";
            version = ua.match(/QQBrowser\/(\d+\.\d+)/) ? RegExp.$1 : "unknown";
        }
        else if (ua.indexOf("UCBrowser") > -1) {
            name = "ucbrowser";
            version = ua.match(/UCBrowser\/(\d+\.\d+)/) ? RegExp.$1 : "unknown";
        }
        else if (ua.indexOf("360EE") > -1) {
            name = "360ee";
            version = ua.match(/360EE\/(\d+\.\d+)/) ? RegExp.$1 : "unknown";
        }
        else if (ua.indexOf("360SE") > -1) {
            name = "360se";
            version = ua.match(/360SE\/(\d+\.\d+)/) ? RegExp.$1 : "unknown";
        }
        else if (ua.indexOf("Maxthon") > -1) {
            name = "maxthon";
            version = ua.match(/Maxthon\/(\d+\.\d+)/) ? RegExp.$1 : "unknown";
        }
    }
    return name + " " + version;
}
export function Date2TimeStr(date, fmt = "yyyy-MM-dd hh:mm:ss") {
    var o = {
        "M+": date.getMonth() + 1,
        "d+": date.getDate(),
        "h+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds(),
        "q+": Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds(),
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    return fmt;
}
export function TimeStr2Date(dateTimeStr) {
    return new Date(dateTimeStr);
}
export function TimeStr2TimeStamp(dateTimeStr) {
    let dateTime = TimeStr2Date(dateTimeStr);
    return dateTime.getTime() / 1000;
}
export function TimeStamp2TimeStr(timeStamp) {
    let dateTime = new Date(timeStamp * 1000);
    return Date2TimeStr(dateTime);
}
export function IsBrowserSupportLocalFonts() {
    if ("queryLocalFonts" in window) {
        return true;
    }
    else {
        return false;
    }
}
export async function GetLocalFontsPermissionState() {
    const { state } = await navigator.permissions.query({
        name: "local-fonts",
    });
    return state;
}
export function Pound2CM(pd) {
    return pd / 28.35;
}
export function PartUpdate(srcObj, destObj, that) {
    Object.keys(srcObj).forEach((key) => {
        destObj[key] = srcObj[key];
        destObj.$set(destObj[key], key, srcObj[key]);
    });
}
export function UpdateConfigData(that, srcObj) {
    Object.keys(srcObj).forEach((key) => {
        that.$set(that.configData, key, srcObj[key]);
    });
}
export function CalcDocName(docName) {
    if (docName === undefined || docName == "") {
        return "空文档_" + GetRandStr();
    }
    return docName;
}
function _PrefixInteger(numStr, n) {
    return (Array(n).join("0") + numStr.toString(16)).slice(-n);
}
export function Color2RGBA(color) {
    if (color.length % 2)
        color = "0" + color;
    let arr = [0x00, 0x00, 0x00, 0x00];
    let fillIndex = 0;
    for (let i = 0; i < color.length; i += 2) {
        let endPos = color.length - i;
        let startPos = endPos - 2;
        let sliceStr = color.substring(startPos, endPos);
        let a = parseInt(sliceStr, 16);
        arr[fillIndex++] = a;
    }
    let rgba = { a: arr[3] / 255.0, r: arr[2], g: arr[1], b: arr[0] };
    return rgba;
}
export function RGBA2Color(rgba) {
    var color = "";
    if (rgba.a !== undefined) {
        color += _PrefixInteger((rgba.a / 1.0) * 255, 2);
    }
    color += _PrefixInteger(rgba.r, 2);
    color += _PrefixInteger(rgba.g, 2);
    color += _PrefixInteger(rgba.b, 2);
    return color;
}
export function RGBA2ColorNum(rgba) {
    return (((rgba.a / 1.0) * 255) << 24) | (parseInt(rgba.r) << 16) | ((rgba.g << 8) | rgba.b);
}
export function ColorNum2RGBAStr(colorNum) {
    let a = (((colorNum >>> 24) & 0xff) / 255.0).toString();
    let r = ((colorNum >>> 16) & 0xff).toString();
    let g = ((colorNum >>> 8) & 0xff).toString();
    let b = (colorNum & 0xff).toString();
    return "rgba(" + r + ", " + g + ", " + b + ", " + a + ")";
}
export function RGBA2RGBAStr(rgba) {
    let a = rgba.a.toString();
    let r = rgba.r.toString();
    let g = rgba.g.toString();
    let b = rgba.b.toString();
    return "rgba(" + r + ", " + g + ", " + b + ", " + a + ")";
}
export function RGBAStr2RGBA(rgbaStr) {
    var rgbaArr = rgbaStr
        .replace(/^(rgb|rgba)\(/, "")
        .replace(/\)$/, "")
        .replace(/\s/g, "")
        .split(",");
    var rgba = {};
    if (rgbaArr.length == 3) {
        rgba.r = parseInt(rgbaArr[0]);
        rgba.g = parseInt(rgbaArr[1]);
        rgba.b = parseInt(rgbaArr[2]);
    }
    else if (rgbaArr.length == 4) {
        rgba.r = parseInt(rgbaArr[0]);
        rgba.g = parseInt(rgbaArr[1]);
        rgba.b = parseInt(rgbaArr[2]);
        rgba.a = parseFloat(rgbaArr[3]);
    }
    return rgba;
}
export function RGBAStr2Color(rgbaStr) {
    let rgba = RGBAStr2RGBA(rgbaStr);
    return RGBA2Color(rgba);
}
export function Color2RGBAStr(color) {
    if (color === undefined) {
        return;
    }
    let rgba = Color2RGBA(color);
    return RGBA2RGBAStr(rgba);
}
export function ToPlainTextByCDATA(text) {
    return "<![CDATA[" + text + "]]>";
}
export function ToPlainTextByEscape(text) {
    function convert(substring) {
        switch (substring) {
            case "<":
                return "&lt;";
            case ">":
                return "&gt;";
            case "&":
                return "&amp;";
            case '"':
                return "&quot;";
            case "'":
                return "&apos;";
        }
        return substring;
    }
    return text.replace(/[<>"&']/g, convert);
}
export function ShowSaveFileDialog(fileName, dataBuffer) {
    let downLink = document.createElement("a");
    downLink.download = fileName;
    let blob = new Blob([dataBuffer]);
    downLink.href = URL.createObjectURL(blob);
    document.body.appendChild(downLink);
    downLink.click();
    document.body.removeChild(downLink);
}
export function GetPrintIframe() {
    var iframe = document.getElementById("printIframe");
    if (iframe === null) {
        iframe = document.createElement("IFRAME");
        iframe.id = "printIframe";
        iframe.setAttribute("style", "position:absolute;width:0px;height:0px;left:-500px;top:-500px;display:none;");
        document.body.appendChild(iframe);
        iframe.print_pages_cache = "";
        iframe.onload = function () {
            setTimeout(function () {
                iframe.focus();
                iframe.contentWindow.print();
            }, 1);
        };
    }
    return iframe;
}
export function GetRandStr() {
    let min = 100000000;
    let max = 999999999;
    let num = Math.floor(Math.random() * (max - min + 1)) + min;
    return num + "";
}
export function PopFileSelector(fileLimit) {
    return new Promise((resolve, reject) => {
        let input = document.createElement("input");
        input.value = "选择文件";
        input.type = "file";
        input.accept = fileLimit;
        input.onchange = function (e) {
            let file = e.target.files[0];
            let file_reader = new FileReader();
            file_reader.onload = (e) => {
                const arrayBuffer = e.target.result;
                const fileName = file.name.split(".").slice(0, -1).join(".");
                resolve({ file: file.name, fileName: fileName, data: arrayBuffer });
            };
            file_reader.readAsArrayBuffer(file);
        };
        input.click();
    });
}
const pinyinFirstLetterDictPolyphone = {
    "19969": "DZ",
    "19975": "WM",
    "19988": "QJ",
    "20048": "YL",
    "20056": "SC",
    "20060": "NM",
    "20094": "QG",
    "20127": "QJ",
    "20167": "QC",
    "20193": "YG",
    "20250": "KH",
    "20256": "ZC",
    "20282": "SC",
    "20285": "QJG",
    "20291": "TD",
    "20314": "YD",
    "20340": "NE",
    "20375": "TD",
    "20389": "YJ",
    "20391": "CZ",
    "20415": "PB",
    "20446": "YS",
    "20447": "SQ",
    "20504": "TC",
    "20608": "KG",
    "20854": "QJ",
    "20857": "ZC",
    "20911": "PF",
    "20985": "AW",
    "21032": "PB",
    "21048": "XQ",
    "21049": "SC",
    "21089": "YS",
    "21119": "JC",
    "21242": "SB",
    "21273": "SC",
    "21305": "YP",
    "21306": "QO",
    "21330": "ZC",
    "21333": "SDC",
    "21345": "QK",
    "21378": "CA",
    "21397": "SC",
    "21414": "XS",
    "21442": "SC",
    "21477": "JG",
    "21480": "TD",
    "21484": "ZS",
    "21494": "YX",
    "21505": "YX",
    "21512": "HG",
    "21523": "XH",
    "21537": "PB",
    "21542": "PF",
    "21549": "KH",
    "21571": "E",
    "21574": "DA",
    "21588": "TD",
    "21589": "O",
    "21618": "ZC",
    "21621": "KHA",
    "21632": "ZJ",
    "21654": "KG",
    "21679": "LKG",
    "21683": "KH",
    "21710": "A",
    "21719": "YH",
    "21734": "WOE",
    "21769": "A",
    "21780": "WN",
    "21804": "XH",
    "21834": "A",
    "21899": "ZD",
    "21903": "RN",
    "21908": "WO",
    "21939": "ZC",
    "21956": "SA",
    "21964": "YA",
    "21970": "TD",
    "22003": "A",
    "22031": "JG",
    "22040": "XS",
    "22060": "ZC",
    "22066": "ZC",
    "22079": "MH",
    "22129": "XJ",
    "22179": "XA",
    "22237": "NJ",
    "22244": "TD",
    "22280": "JQ",
    "22300": "YH",
    "22313": "XW",
    "22331": "YQ",
    "22343": "YJ",
    "22351": "PH",
    "22395": "DC",
    "22412": "TD",
    "22484": "PB",
    "22500": "PB",
    "22534": "ZD",
    "22549": "DH",
    "22561": "PB",
    "22612": "TD",
    "22771": "KQ",
    "22831": "HB",
    "22841": "JG",
    "22855": "QJ",
    "22865": "XQ",
    "23013": "ML",
    "23081": "WM",
    "23487": "SX",
    "23558": "QJ",
    "23561": "YW",
    "23586": "YW",
    "23614": "YW",
    "23615": "SN",
    "23631": "PB",
    "23646": "ZS",
    "23663": "ZT",
    "23673": "YG",
    "23762": "TD",
    "23769": "ZS",
    "23780": "QJ",
    "23884": "QK",
    "24055": "XH",
    "24113": "DC",
    "24162": "ZC",
    "24191": "GA",
    "24273": "QJ",
    "24324": "NL",
    "24377": "TD",
    "24378": "QJ",
    "24439": "PF",
    "24554": "ZS",
    "24683": "TD",
    "24694": "WE",
    "24733": "LK",
    "24925": "TN",
    "25094": "ZG",
    "25100": "XQ",
    "25103": "XH",
    "25153": "PB",
    "25170": "PB",
    "25179": "KG",
    "25203": "PB",
    "25240": "ZS",
    "25282": "FB",
    "25303": "NA",
    "25324": "KG",
    "25341": "ZY",
    "25373": "WZ",
    "25375": "XJ",
    "25384": "A",
    "25457": "A",
    "25528": "SD",
    "25530": "SC",
    "25552": "TD",
    "25774": "ZC",
    "25874": "ZC",
    "26044": "YW",
    "26080": "WM",
    "26292": "PB",
    "26333": "PB",
    "26355": "ZY",
    "26366": "CZ",
    "26397": "ZC",
    "26399": "QJ",
    "26415": "ZS",
    "26451": "SB",
    "26526": "ZC",
    "26552": "JG",
    "26561": "TD",
    "26588": "JG",
    "26597": "CZ",
    "26629": "ZS",
    "26638": "YL",
    "26646": "XQ",
    "26653": "KG",
    "26657": "XJ",
    "26727": "HG",
    "26894": "ZC",
    "26937": "ZS",
    "26946": "ZC",
    "26999": "KJ",
    "27099": "KJ",
    "27449": "YQ",
    "27481": "XS",
    "27542": "ZS",
    "27663": "ZS",
    "27748": "TS",
    "27784": "SC",
    "27788": "ZD",
    "27795": "TD",
    "27812": "O",
    "27850": "PB",
    "27852": "MB",
    "27895": "SL",
    "27898": "PL",
    "27973": "QJ",
    "27981": "KH",
    "27986": "HX",
    "27994": "XJ",
    "28044": "YC",
    "28065": "WG",
    "28177": "SM",
    "28267": "QJ",
    "28291": "KH",
    "28337": "ZQ",
    "28463": "TL",
    "28548": "DC",
    "28601": "TD",
    "28689": "PB",
    "28805": "JG",
    "28820": "QG",
    "28846": "PB",
    "28952": "TD",
    "28975": "ZC",
    "29100": "A",
    "29325": "QJ",
    "29575": "SL",
    "29602": "FB",
    "30010": "TD",
    "30044": "CX",
    "30058": "PF",
    "30091": "YSP",
    "30111": "YN",
    "30229": "XJ",
    "30427": "SC",
    "30465": "SX",
    "30631": "YQ",
    "30655": "QJ",
    "30684": "QJG",
    "30707": "SD",
    "30729": "XH",
    "30796": "LG",
    "30917": "PB",
    "31074": "NM",
    "31085": "JZ",
    "31109": "SC",
    "31181": "ZC",
    "31192": "MLB",
    "31293": "JQ",
    "31400": "YX",
    "31584": "YJ",
    "31896": "ZN",
    "31909": "ZY",
    "31995": "XJ",
    "32321": "PF",
    "32327": "ZY",
    "32418": "HG",
    "32420": "XQ",
    "32421": "HG",
    "32438": "LG",
    "32473": "GJ",
    "32488": "TD",
    "32521": "QJ",
    "32527": "PB",
    "32562": "ZSQ",
    "32564": "JZ",
    "32735": "ZD",
    "32793": "PB",
    "33071": "PF",
    "33098": "XL",
    "33100": "YA",
    "33152": "PB",
    "33261": "CX",
    "33324": "BP",
    "33333": "TD",
    "33406": "YA",
    "33426": "WM",
    "33432": "PB",
    "33445": "JG",
    "33486": "ZN",
    "33493": "TS",
    "33507": "QJ",
    "33540": "QJ",
    "33544": "ZC",
    "33564": "XQ",
    "33617": "YT",
    "33632": "QJ",
    "33636": "XH",
    "33637": "YX",
    "33694": "WG",
    "33705": "PF",
    "33728": "YW",
    "33882": "SR",
    "34067": "WM",
    "34074": "YW",
    "34121": "QJ",
    "34255": "ZC",
    "34259": "XL",
    "34425": "JH",
    "34430": "XH",
    "34485": "KH",
    "34503": "YS",
    "34532": "HG",
    "34552": "XS",
    "34558": "YE",
    "34593": "ZL",
    "34660": "YQ",
    "34892": "XH",
    "34928": "SC",
    "34999": "QJ",
    "35048": "PB",
    "35059": "SC",
    "35098": "ZC",
    "35203": "TQ",
    "35265": "JX",
    "35299": "JX",
    "35782": "SZ",
    "35828": "YS",
    "35830": "E",
    "35843": "TD",
    "35895": "YG",
    "35977": "MH",
    "36158": "JG",
    "36228": "QJ",
    "36426": "XQ",
    "36466": "DC",
    "36710": "JC",
    "36711": "ZYG",
    "36767": "PB",
    "36866": "SK",
    "36951": "YW",
    "37034": "YX",
    "37063": "XH",
    "37218": "ZC",
    "37325": "ZC",
    "38063": "PB",
    "38079": "TD",
    "38085": "QY",
    "38107": "DC",
    "38116": "TD",
    "38123": "YD",
    "38224": "HG",
    "38241": "XTC",
    "38271": "ZC",
    "38415": "YE",
    "38426": "KH",
    "38461": "YD",
    "38463": "AE",
    "38466": "PB",
    "38477": "XJ",
    "38518": "YT",
    "38551": "WK",
    "38585": "ZC",
    "38704": "XS",
    "38739": "LJ",
    "38761": "GJ",
    "38808": "SQ",
    "39048": "JG",
    "39049": "XJ",
    "39052": "HG",
    "39076": "CZ",
    "39271": "XT",
    "39534": "TD",
    "39552": "TD",
    "39584": "PB",
    "39647": "SB",
    "39730": "LG",
    "39748": "TPB",
    "40109": "ZQ",
    "40479": "ND",
    "40516": "HG",
    "40536": "HG",
    "40583": "QJ",
    "40765": "YQ",
    "40784": "QJ",
    "40840": "YK",
    "40863": "QJG",
};
const pinyinFirstLetterDict = "YDYQSXMWZSSXJBYMGCCZQPSSQBYCDSCDQLDYLYBSSJGYZZJJFKCCLZDHWDWZJLJPFYYNWJJTMYHZWZHFLZPPQHGSCYYYNJQYXXGJHHSDSJNKKTMOMLCRXYPSNQSECCQZGGLLYJLMYZZSECYKYYHQWJSSGGYXYZYJWWKDJHYCHMYXJTLXJYQBYXZLDWRDJRWYSRLDZJPCBZJJBRCFTLECZSTZFXXZHTRQHYBDLYCZSSYMMRFMYQZPWWJJYFCRWFDFZQPYDDWYXKYJAWJFFXYPSFTZYHHYZYSWCJYXSCLCXXWZZXNBGNNXBXLZSZSBSGPYSYZDHMDZBQBZCWDZZYYTZHBTSYYBZGNTNXQYWQSKBPHHLXGYBFMJEBJHHGQTJCYSXSTKZHLYCKGLYSMZXYALMELDCCXGZYRJXSDLTYZCQKCNNJWHJTZZCQLJSTSTBNXBTYXCEQXGKWJYFLZQLYHYXSPSFXLMPBYSXXXYDJCZYLLLSJXFHJXPJBTFFYABYXBHZZBJYZLWLCZGGBTSSMDTJZXPTHYQTGLJSCQFZKJZJQNLZWLSLHDZBWJNCJZYZSQQYCQYRZCJJWYBRTWPYFTWEXCSKDZCTBZHYZZYYJXZCFFZZMJYXXSDZZOTTBZLQWFCKSZSXFYRLNYJMBDTHJXSQQCCSBXYYTSYFBXDZTGBCNSLCYZZPSAZYZZSCJCSHZQYDXLBPJLLMQXTYDZXSQJTZPXLCGLQTZWJBHCTSYJSFXYEJJTLBGXSXJMYJQQPFZASYJNTYDJXKJCDJSZCBARTDCLYJQMWNQNCLLLKBYBZZSYHQQLTWLCCXTXLLZNTYLNEWYZYXCZXXGRKRMTCNDNJTSYYSSDQDGHSDBJGHRWRQLYBGLXHLGTGXBQJDZPYJSJYJCTMRNYMGRZJCZGJMZMGXMPRYXKJNYMSGMZJYMKMFXMLDTGFBHCJHKYLPFMDXLQJJSMTQGZSJLQDLDGJYCALCMZCSDJLLNXDJFFFFJCZFMZFFPFKHKGDPSXKTACJDHHZDDCRRCFQYJKQCCWJDXHWJLYLLZGCFCQDSMLZPBJJPLSBCJGGDCKKDEZSQCCKJGCGKDJTJDLZYCXKLQSCGJCLTFPCQCZGWPJDQYZJJBYJHSJDZWGFSJGZKQCCZLLPSPKJGQJHZZLJPLGJGJJTHJJYJZCZMLZLYQBGJWMLJKXZDZNJQSYZMLJLLJKYWXMKJLHSKJGBMCLYYMKXJQLBMLLKMDXXKWYXYSLMLPSJQQJQXYXFJTJDXMXXLLCXQBSYJBGWYMBGGBCYXPJYGPEPFGDJGBHBNSQJYZJKJKHXQFGQZKFHYGKHDKLLSDJQXPQYKYBNQSXQNSZSWHBSXWHXWBZZXDMNSJBSBKBBZKLYLXGWXDRWYQZMYWSJQLCJXXJXKJEQXSCYETLZHLYYYSDZPAQYZCMTLSHTZCFYZYXYLJSDCJQAGYSLCQLYYYSHMRQQKLDXZSCSSSYDYCJYSFSJBFRSSZQSBXXPXJYSDRCKGJLGDKZJZBDKTCSYQPYHSTCLDJDHMXMCGXYZHJDDTMHLTXZXYLYMOHYJCLTYFBQQXPFBDFHHTKSQHZYYWCNXXCRWHOWGYJLEGWDQCWGFJYCSNTMYTOLBYGWQWESJPWNMLRYDZSZTXYQPZGCWXHNGPYXSHMYQJXZTDPPBFYHZHTJYFDZWKGKZBLDNTSXHQEEGZZYLZMMZYJZGXZXKHKSTXNXXWYLYAPSTHXDWHZYMPXAGKYDXBHNHXKDPJNMYHYLPMGOCSLNZHKXXLPZZLBMLSFBHHGYGYYGGBHSCYAQTYWLXTZQCEZYDQDQMMHTKLLSZHLSJZWFYHQSWSCWLQAZYNYTLSXTHAZNKZZSZZLAXXZWWCTGQQTDDYZTCCHYQZFLXPSLZYGPZSZNGLNDQTBDLXGTCTAJDKYWNSYZLJHHZZCWNYYZYWMHYCHHYXHJKZWSXHZYXLYSKQYSPSLYZWMYPPKBYGLKZHTYXAXQSYSHXASMCHKDSCRSWJPWXSGZJLWWSCHSJHSQNHCSEGNDAQTBAALZZMSSTDQJCJKTSCJAXPLGGXHHGXXZCXPDMMHLDGTYBYSJMXHMRCPXXJZCKZXSHMLQXXTTHXWZFKHCCZDYTCJYXQHLXDHYPJQXYLSYYDZOZJNYXQEZYSQYAYXWYPDGXDDXSPPYZNDLTWRHXYDXZZJHTCXMCZLHPYYYYMHZLLHNXMYLLLMDCPPXHMXDKYCYRDLTXJCHHZZXZLCCLYLNZSHZJZZLNNRLWHYQSNJHXYNTTTKYJPYCHHYEGKCTTWLGQRLGGTGTYGYHPYHYLQYQGCWYQKPYYYTTTTLHYHLLTYTTSPLKYZXGZWGPYDSSZZDQXSKCQNMJJZZBXYQMJRTFFBTKHZKBXLJJKDXJTLBWFZPPTKQTZTGPDGNTPJYFALQMKGXBDCLZFHZCLLLLADPMXDJHLCCLGYHDZFGYDDGCYYFGYDXKSSEBDHYKDKDKHNAXXYBPBYYHXZQGAFFQYJXDMLJCSQZLLPCHBSXGJYNDYBYQSPZWJLZKSDDTACTBXZDYZYPJZQSJNKKTKNJDJGYYPGTLFYQKASDNTCYHBLWDZHBBYDWJRYGKZYHEYYFJMSDTYFZJJHGCXPLXHLDWXXJKYTCYKSSSMTWCTTQZLPBSZDZWZXGZAGYKTYWXLHLSPBCLLOQMMZSSLCMBJCSZZKYDCZJGQQDSMCYTZQQLWZQZXSSFPTTFQMDDZDSHDTDWFHTDYZJYQJQKYPBDJYYXTLJHDRQXXXHAYDHRJLKLYTWHLLRLLRCXYLBWSRSZZSYMKZZHHKYHXKSMDSYDYCJPBZBSQLFCXXXNXKXWYWSDZYQOGGQMMYHCDZTTFJYYBGSTTTYBYKJDHKYXBELHTYPJQNFXFDYKZHQKZBYJTZBXHFDXKDASWTAWAJLDYJSFHBLDNNTNQJTJNCHXFJSRFWHZFMDRYJYJWZPDJKZYJYMPCYZNYNXFBYTFYFWYGDBNZZZDNYTXZEMMQBSQEHXFZMBMFLZZSRXYMJGSXWZJSPRYDJSJGXHJJGLJJYNZZJXHGXKYMLPYYYCXYTWQZSWHWLYRJLPXSLSXMFSWWKLCTNXNYNPSJSZHDZEPTXMYYWXYYSYWLXJQZQXZDCLEEELMCPJPCLWBXSQHFWWTFFJTNQJHJQDXHWLBYZNFJLALKYYJLDXHHYCSTYYWNRJYXYWTRMDRQHWQCMFJDYZMHMYYXJWMYZQZXTLMRSPWWCHAQBXYGZYPXYYRRCLMPYMGKSJSZYSRMYJSNXTPLNBAPPYPYLXYYZKYNLDZYJZCZNNLMZHHARQMPGWQTZMXXMLLHGDZXYHXKYXYCJMFFYYHJFSBSSQLXXNDYCANNMTCJCYPRRNYTYQNYYMBMSXNDLYLYSLJRLXYSXQMLLYZLZJJJKYZZCSFBZXXMSTBJGNXYZHLXNMCWSCYZYFZLXBRNNNYLBNRTGZQYSATSWRYHYJZMZDHZGZDWYBSSCSKXSYHYTXXGCQGXZZSHYXJSCRHMKKBXCZJYJYMKQHZJFNBHMQHYSNJNZYBKNQMCLGQHWLZNZSWXKHLJHYYBQLBFCDSXDLDSPFZPSKJYZWZXZDDXJSMMEGJSCSSMGCLXXKYYYLNYPWWWGYDKZJGGGZGGSYCKNJWNJPCXBJJTQTJWDSSPJXZXNZXUMELPXFSXTLLXCLJXJJLJZXCTPSWXLYDHLYQRWHSYCSQYYBYAYWJJJQFWQCQQCJQGXALDBZZYJGKGXPLTZYFXJLTPADKYQHPMATLCPDCKBMTXYBHKLENXDLEEGQDYMSAWHZMLJTWYGXLYQZLJEEYYBQQFFNLYXRDSCTGJGXYYNKLLYQKCCTLHJLQMKKZGCYYGLLLJDZGYDHZWXPYSJBZKDZGYZZHYWYFQYTYZSZYEZZLYMHJJHTSMQWYZLKYYWZCSRKQYTLTDXWCTYJKLWSQZWBDCQYNCJSRSZJLKCDCDTLZZZACQQZZDDXYPLXZBQJYLZLLLQDDZQJYJYJZYXNYYYNYJXKXDAZWYRDLJYYYRJLXLLDYXJCYWYWNQCCLDDNYYYNYCKCZHXXCCLGZQJGKWPPCQQJYSBZZXYJSQPXJPZBSBDSFNSFPZXHDWZTDWPPTFLZZBZDMYYPQJRSDZSQZSQXBDGCPZSWDWCSQZGMDHZXMWWFYBPDGPHTMJTHZSMMBGZMBZJCFZWFZBBZMQCFMBDMCJXLGPNJBBXGYHYYJGPTZGZMQBQTCGYXJXLWZKYDPDYMGCFTPFXYZTZXDZXTGKMTYBBCLBJASKYTSSQYYMSZXFJEWLXLLSZBQJJJAKLYLXLYCCTSXMCWFKKKBSXLLLLJYXTYLTJYYTDPJHNHNNKBYQNFQYYZBYYESSESSGDYHFHWTCJBSDZZTFDMXHCNJZYMQWSRYJDZJQPDQBBSTJGGFBKJBXTGQHNGWJXJGDLLTHZHHYYYYYYSXWTYYYCCBDBPYPZYCCZYJPZYWCBDLFWZCWJDXXHYHLHWZZXJTCZLCDPXUJCZZZLYXJJTXPHFXWPYWXZPTDZZBDZCYHJHMLXBQXSBYLRDTGJRRCTTTHYTCZWMXFYTWWZCWJWXJYWCSKYBZSCCTZQNHXNWXXKHKFHTSWOCCJYBCMPZZYKBNNZPBZHHZDLSYDDYTYFJPXYNGFXBYQXCBHXCPSXTYZDMKYSNXSXLHKMZXLYHDHKWHXXSSKQYHHCJYXGLHZXCSNHEKDTGZXQYPKDHEXTYKCNYMYYYPKQYYYKXZLTHJQTBYQHXBMYHSQCKWWYLLHCYYLNNEQXQWMCFBDCCMLJGGXDQKTLXKGNQCDGZJWYJJLYHHQTTTNWCHMXCXWHWSZJYDJCCDBQCDGDNYXZTHCQRXCBHZTQCBXWGQWYYBXHMBYMYQTYEXMQKYAQYRGYZSLFYKKQHYSSQYSHJGJCNXKZYCXSBXYXHYYLSTYCXQTHYSMGSCPMMGCCCCCMTZTASMGQZJHKLOSQYLSWTMXSYQKDZLJQQYPLSYCZTCQQPBBQJZCLPKHQZYYXXDTDDTSJCXFFLLCHQXMJLWCJCXTSPYCXNDTJSHJWXDQQJSKXYAMYLSJHMLALYKXCYYDMNMDQMXMCZNNCYBZKKYFLMCHCMLHXRCJJHSYLNMTJZGZGYWJXSRXCWJGJQHQZDQJDCJJZKJKGDZQGJJYJYLXZXXCDQHHHEYTMHLFSBDJSYYSHFYSTCZQLPBDRFRZTZYKYWHSZYQKWDQZRKMSYNBCRXQBJYFAZPZZEDZCJYWBCJWHYJBQSZYWRYSZPTDKZPFPBNZTKLQYHBBZPNPPTYZZYBQNYDCPJMMCYCQMCYFZZDCMNLFPBPLNGQJTBTTNJZPZBBZNJKLJQYLNBZQHKSJZNGGQSZZKYXSHPZSNBCGZKDDZQANZHJKDRTLZLSWJLJZLYWTJNDJZJHXYAYNCBGTZCSSQMNJPJYTYSWXZFKWJQTKHTZPLBHSNJZSYZBWZZZZLSYLSBJHDWWQPSLMMFBJDWAQYZTCJTBNNWZXQXCDSLQGDSDPDZHJTQQPSWLYYJZLGYXYZLCTCBJTKTYCZJTQKBSJLGMGZDMCSGPYNJZYQYYKNXRPWSZXMTNCSZZYXYBYHYZAXYWQCJTLLCKJJTJHGDXDXYQYZZBYWDLWQCGLZGJGQRQZCZSSBCRPCSKYDZNXJSQGXSSJMYDNSTZTPBDLTKZWXQWQTZEXNQCZGWEZKSSBYBRTSSSLCCGBPSZQSZLCCGLLLZXHZQTHCZMQGYZQZNMCOCSZJMMZSQPJYGQLJYJPPLDXRGZYXCCSXHSHGTZNLZWZKJCXTCFCJXLBMQBCZZWPQDNHXLJCTHYZLGYLNLSZZPCXDSCQQHJQKSXZPBAJYEMSMJTZDXLCJYRYYNWJBNGZZTMJXLTBSLYRZPYLSSCNXPHLLHYLLQQZQLXYMRSYCXZLMMCZLTZSDWTJJLLNZGGQXPFSKYGYGHBFZPDKMWGHCXMSGDXJMCJZDYCABXJDLNBCDQYGSKYDQTXDJJYXMSZQAZDZFSLQXYJSJZYLBTXXWXQQZBJZUFBBLYLWDSLJHXJYZJWTDJCZFQZQZZDZSXZZQLZCDZFJHYSPYMPQZMLPPLFFXJJNZZYLSJEYQZFPFZKSYWJJJHRDJZZXTXXGLGHYDXCSKYSWMMZCWYBAZBJKSHFHJCXMHFQHYXXYZFTSJYZFXYXPZLCHMZMBXHZZSXYFYMNCWDABAZLXKTCSHHXKXJJZJSTHYGXSXYYHHHJWXKZXSSBZZWHHHCWTZZZPJXSNXQQJGZYZYWLLCWXZFXXYXYHXMKYYSWSQMNLNAYCYSPMJKHWCQHYLAJJMZXHMMCNZHBHXCLXTJPLTXYJHDYYLTTXFSZHYXXSJBJYAYRSMXYPLCKDUYHLXRLNLLSTYZYYQYGYHHSCCSMZCTZQXKYQFPYYRPFFLKQUNTSZLLZMWWTCQQYZWTLLMLMPWMBZSSTZRBPDDTLQJJBXZCSRZQQYGWCSXFWZLXCCRSZDZMCYGGDZQSGTJSWLJMYMMZYHFBJDGYXCCPSHXNZCSBSJYJGJMPPWAFFYFNXHYZXZYLREMZGZCYZSSZDLLJCSQFNXZKPTXZGXJJGFMYYYSNBTYLBNLHPFZDCYFBMGQRRSSSZXYSGTZRNYDZZCDGPJAFJFZKNZBLCZSZPSGCYCJSZLMLRSZBZZLDLSLLYSXSQZQLYXZLSKKBRXBRBZCYCXZZZEEYFGKLZLYYHGZSGZLFJHGTGWKRAAJYZKZQTSSHJJXDCYZUYJLZYRZDQQHGJZXSSZBYKJPBFRTJXLLFQWJHYLQTYMBLPZDXTZYGBDHZZRBGXHWNJTJXLKSCFSMWLSDQYSJTXKZSCFWJLBXFTZLLJZLLQBLSQMQQCGCZFPBPHZCZJLPYYGGDTGWDCFCZQYYYQYSSCLXZSKLZZZGFFCQNWGLHQYZJJCZLQZZYJPJZZBPDCCMHJGXDQDGDLZQMFGPSYTSDYFWWDJZJYSXYYCZCYHZWPBYKXRYLYBHKJKSFXTZJMMCKHLLTNYYMSYXYZPYJQYCSYCWMTJJKQYRHLLQXPSGTLYYCLJSCPXJYZFNMLRGJJTYZBXYZMSJYJHHFZQMSYXRSZCWTLRTQZSSTKXGQKGSPTGCZNJSJCQCXHMXGGZTQYDJKZDLBZSXJLHYQGGGTHQSZPYHJHHGYYGKGGCWJZZYLCZLXQSFTGZSLLLMLJSKCTBLLZZSZMMNYTPZSXQHJCJYQXYZXZQZCPSHKZZYSXCDFGMWQRLLQXRFZTLYSTCTMJCXJJXHJNXTNRZTZFQYHQGLLGCXSZSJDJLJCYDSJTLNYXHSZXCGJZYQPYLFHDJSBPCCZHJJJQZJQDYBSSLLCMYTTMQTBHJQNNYGKYRQYQMZGCJKPDCGMYZHQLLSLLCLMHOLZGDYYFZSLJCQZLYLZQJESHNYLLJXGJXLYSYYYXNBZLJSSZCQQCJYLLZLTJYLLZLLBNYLGQCHXYYXOXCXQKYJXXXYKLXSXXYQXCYKQXQCSGYXXYQXYGYTQOHXHXPYXXXULCYEYCHZZCBWQBBWJQZSCSZSSLZYLKDESJZWMYMCYTSDSXXSCJPQQSQYLYYZYCMDJDZYWCBTJSYDJKCYDDJLBDJJSODZYSYXQQYXDHHGQQYQHDYXWGMMMAJDYBBBPPBCMUUPLJZSMTXERXJMHQNUTPJDCBSSMSSSTKJTSSMMTRCPLZSZMLQDSDMJMQPNQDXCFYNBFSDQXYXHYAYKQYDDLQYYYSSZBYDSLNTFQTZQPZMCHDHCZCWFDXTMYQSPHQYYXSRGJCWTJTZZQMGWJJTJHTQJBBHWZPXXHYQFXXQYWYYHYSCDYDHHQMNMTMWCPBSZPPZZGLMZFOLLCFWHMMSJZTTDHZZYFFYTZZGZYSKYJXQYJZQBHMBZZLYGHGFMSHPZFZSNCLPBQSNJXZSLXXFPMTYJYGBXLLDLXPZJYZJYHHZCYWHJYLSJEXFSZZYWXKZJLUYDTMLYMQJPWXYHXSKTQJEZRPXXZHHMHWQPWQLYJJQJJZSZCPHJLCHHNXJLQWZJHBMZYXBDHHYPZLHLHLGFWLCHYYTLHJXCJMSCPXSTKPNHQXSRTYXXTESYJCTLSSLSTDLLLWWYHDHRJZSFGXTSYCZYNYHTDHWJSLHTZDQDJZXXQHGYLTZPHCSQFCLNJTCLZPFSTPDYNYLGMJLLYCQHYSSHCHYLHQYQTMZYPBYWRFQYKQSYSLZDQJMPXYYSSRHZJNYWTQDFZBWWTWWRXCWHGYHXMKMYYYQMSMZHNGCEPMLQQMTCWCTMMPXJPJJHFXYYZSXZHTYBMSTSYJTTQQQYYLHYNPYQZLCYZHZWSMYLKFJXLWGXYPJYTYSYXYMZCKTTWLKSMZSYLMPWLZWXWQZSSAQSYXYRHSSNTSRAPXCPWCMGDXHXZDZYFJHGZTTSBJHGYZSZYSMYCLLLXBTYXHBBZJKSSDMALXHYCFYGMQYPJYCQXJLLLJGSLZGQLYCJCCZOTYXMTMTTLLWTGPXYMZMKLPSZZZXHKQYSXCTYJZYHXSHYXZKXLZWPSQPYHJWPJPWXQQYLXSDHMRSLZZYZWTTCYXYSZZSHBSCCSTPLWSSCJCHNLCGCHSSPHYLHFHHXJSXYLLNYLSZDHZXYLSXLWZYKCLDYAXZCMDDYSPJTQJZLNWQPSSSWCTSTSZLBLNXSMNYYMJQBQHRZWTYYDCHQLXKPZWBGQYBKFCMZWPZLLYYLSZYDWHXPSBCMLJBSCGBHXLQHYRLJXYSWXWXZSLDFHLSLYNJLZYFLYJYCDRJLFSYZFSLLCQYQFGJYHYXZLYLMSTDJCYHBZLLNWLXXYGYYHSMGDHXXHHLZZJZXCZZZCYQZFNGWPYLCPKPYYPMCLQKDGXZGGWQBDXZZKZFBXXLZXJTPJPTTBYTSZZDWSLCHZHSLTYXHQLHYXXXYYZYSWTXZKHLXZXZPYHGCHKCFSYHUTJRLXFJXPTZTWHPLYXFCRHXSHXKYXXYHZQDXQWULHYHMJTBFLKHTXCWHJFWJCFPQRYQXCYYYQYGRPYWSGSUNGWCHKZDXYFLXXHJJBYZWTSXXNCYJJYMSWZJQRMHXZWFQSYLZJZGBHYNSLBGTTCSYBYXXWXYHXYYXNSQYXMQYWRGYQLXBBZLJSYLPSYTJZYHYZAWLRORJMKSCZJXXXYXCHDYXRYXXJDTSQFXLYLTSFFYXLMTYJMJUYYYXLTZCSXQZQHZXLYYXZHDNBRXXXJCTYHLBRLMBRLLAXKYLLLJLYXXLYCRYLCJTGJCMTLZLLCYZZPZPCYAWHJJFYBDYYZSMPCKZDQYQPBPCJPDCYZMDPBCYYDYCNNPLMTMLRMFMMGWYZBSJGYGSMZQQQZTXMKQWGXLLPJGZBQCDJJJFPKJKCXBLJMSWMDTQJXLDLPPBXCWRCQFBFQJCZAHZGMYKPHYYHZYKNDKZMBPJYXPXYHLFPNYYGXJDBKXNXHJMZJXSTRSTLDXSKZYSYBZXJLXYSLBZYSLHXJPFXPQNBYLLJQKYGZMCYZZYMCCSLCLHZFWFWYXZMWSXTYNXJHPYYMCYSPMHYSMYDYSHQYZCHMJJMZCAAGCFJBBHPLYZYLXXSDJGXDHKXXTXXNBHRMLYJSLTXMRHNLXQJXYZLLYSWQGDLBJHDCGJYQYCMHWFMJYBMBYJYJWYMDPWHXQLDYGPDFXXBCGJSPCKRSSYZJMSLBZZJFLJJJLGXZGYXYXLSZQYXBEXYXHGCXBPLDYHWETTWWCJMBTXCHXYQXLLXFLYXLLJLSSFWDPZSMYJCLMWYTCZPCHQEKCQBWLCQYDPLQPPQZQFJQDJHYMMCXTXDRMJWRHXCJZYLQXDYYNHYYHRSLSRSYWWZJYMTLTLLGTQCJZYABTCKZCJYCCQLJZQXALMZYHYWLWDXZXQDLLQSHGPJFJLJHJABCQZDJGTKHSSTCYJLPSWZLXZXRWGLDLZRLZXTGSLLLLZLYXXWGDZYGBDPHZPBRLWSXQBPFDWOFMWHLYPCBJCCLDMBZPBZZLCYQXLDOMZBLZWPDWYYGDSTTHCSQSCCRSSSYSLFYBFNTYJSZDFNDPDHDZZMBBLSLCMYFFGTJJQWFTMTPJWFNLBZCMMJTGBDZLQLPYFHYYMJYLSDCHDZJWJCCTLJCLDTLJJCPDDSQDSSZYBNDBJLGGJZXSXNLYCYBJXQYCBYLZCFZPPGKCXZDZFZTJJFJSJXZBNZYJQTTYJYHTYCZHYMDJXTTMPXSPLZCDWSLSHXYPZGTFMLCJTYCBPMGDKWYCYZCDSZZYHFLYCTYGWHKJYYLSJCXGYWJCBLLCSNDDBTZBSCLYZCZZSSQDLLMQYYHFSLQLLXFTYHABXGWNYWYYPLLSDLDLLBJCYXJZMLHLJDXYYQYTDLLLBUGBFDFBBQJZZMDPJHGCLGMJJPGAEHHBWCQXAXHHHZCHXYPHJAXHLPHJPGPZJQCQZGJJZZUZDMQYYBZZPHYHYBWHAZYJHYKFGDPFQSDLZMLJXKXGALXZDAGLMDGXMWZQYXXDXXPFDMMSSYMPFMDMMKXKSYZYSHDZKXSYSMMZZZMSYDNZZCZXFPLSTMZDNMXCKJMZTYYMZMZZMSXHHDCZJEMXXKLJSTLWLSQLYJZLLZJSSDPPMHNLZJCZYHMXXHGZCJMDHXTKGRMXFWMCGMWKDTKSXQMMMFZZYDKMSCLCMPCGMHSPXQPZDSSLCXKYXTWLWJYAHZJGZQMCSNXYYMMPMLKJXMHLMLQMXCTKZMJQYSZJSYSZHSYJZJCDAJZYBSDQJZGWZQQXFKDMSDJLFWEHKZQKJPEYPZYSZCDWYJFFMZZYLTTDZZEFMZLBNPPLPLPEPSZALLTYLKCKQZKGENQLWAGYXYDPXLHSXQQWQCQXQCLHYXXMLYCCWLYMQYSKGCHLCJNSZKPYZKCQZQLJPDMDZHLASXLBYDWQLWDNBQCRYDDZTJYBKBWSZDXDTNPJDTCTQDFXQQMGNXECLTTBKPWSLCTYQLPWYZZKLPYGZCQQPLLKCCYLPQMZCZQCLJSLQZDJXLDDHPZQDLJJXZQDXYZQKZLJCYQDYJPPYPQYKJYRMPCBYMCXKLLZLLFQPYLLLMBSGLCYSSLRSYSQTMXYXZQZFDZUYSYZTFFMZZSMZQHZSSCCMLYXWTPZGXZJGZGSJSGKDDHTQGGZLLBJDZLCBCHYXYZHZFYWXYZYMSDBZZYJGTSMTFXQYXQSTDGSLNXDLRYZZLRYYLXQHTXSRTZNGZXBNQQZFMYKMZJBZYMKBPNLYZPBLMCNQYZZZSJZHJCTZKHYZZJRDYZHNPXGLFZTLKGJTCTSSYLLGZRZBBQZZKLPKLCZYSSUYXBJFPNJZZXCDWXZYJXZZDJJKGGRSRJKMSMZJLSJYWQSKYHQJSXPJZZZLSNSHRNYPZTWCHKLPSRZLZXYJQXQKYSJYCZTLQZYBBYBWZPQDWWYZCYTJCJXCKCWDKKZXSGKDZXWWYYJQYYTCYTDLLXWKCZKKLCCLZCQQDZLQLCSFQCHQHSFSMQZZLNBJJZBSJHTSZDYSJQJPDLZCDCWJKJZZLPYCGMZWDJJBSJQZSYZYHHXJPBJYDSSXDZNCGLQMBTSFSBPDZDLZNFGFJGFSMPXJQLMBLGQCYYXBQKDJJQYRFKZTJDHCZKLBSDZCFJTPLLJGXHYXZCSSZZXSTJYGKGCKGYOQXJPLZPBPGTGYJZGHZQZZLBJLSQFZGKQQJZGYCZBZQTLDXRJXBSXXPZXHYZYCLWDXJJHXMFDZPFZHQHQMQGKSLYHTYCGFRZGNQXCLPDLBZCSCZQLLJBLHBZCYPZZPPDYMZZSGYHCKCPZJGSLJLNSCDSLDLXBMSTLDDFJMKDJDHZLZXLSZQPQPGJLLYBDSZGQLBZLSLKYYHZTTNTJYQTZZPSZQZTLLJTYYLLQLLQYZQLBDZLSLYYZYMDFSZSNHLXZNCZQZPBWSKRFBSYZMTHBLGJPMCZZLSTLXSHTCSYZLZBLFEQHLXFLCJLYLJQCBZLZJHHSSTBRMHXZHJZCLXFNBGXGTQJCZTMSFZKJMSSNXLJKBHSJXNTNLZDNTLMSJXGZJYJCZXYJYJWRWWQNZTNFJSZPZSHZJFYRDJSFSZJZBJFZQZZHZLXFYSBZQLZSGYFTZDCSZXZJBQMSZKJRHYJZCKMJKHCHGTXKXQGLXPXFXTRTYLXJXHDTSJXHJZJXZWZLCQSBTXWXGXTXXHXFTSDKFJHZYJFJXRZSDLLLTQSQQZQWZXSYQTWGWBZCGZLLYZBCLMQQTZHZXZXLJFRMYZFLXYSQXXJKXRMQDZDMMYYBSQBHGZMWFWXGMXLZPYYTGZYCCDXYZXYWGSYJYZNBHPZJSQSYXSXRTFYZGRHZTXSZZTHCBFCLSYXZLZQMZLMPLMXZJXSFLBYZMYQHXJSXRXSQZZZSSLYFRCZJRCRXHHZXQYDYHXSJJHZCXZBTYNSYSXJBQLPXZQPYMLXZKYXLXCJLCYSXXZZLXDLLLJJYHZXGYJWKJRWYHCPSGNRZLFZWFZZNSXGXFLZSXZZZBFCSYJDBRJKRDHHGXJLJJTGXJXXSTJTJXLYXQFCSGSWMSBCTLQZZWLZZKXJMLTMJYHSDDBXGZHDLBMYJFRZFSGCLYJBPMLYSMSXLSZJQQHJZFXGFQFQBPXZGYYQXGZTCQWYLTLGWSGWHRLFSFGZJMGMGBGTJFSYZZGZYZAFLSSPMLPFLCWBJZCLJJMZLPJJLYMQDMYYYFBGYGYZMLYZDXQYXRQQQHSYYYQXYLJTYXFSFSLLGNQCYHYCWFHCCCFXPYLYPLLZYXXXXXKQHHXSHJZCFZSCZJXCPZWHHHHHAPYLQALPQAFYHXDYLUKMZQGGGDDESRNNZLTZGCHYPPYSQJJHCLLJTOLNJPZLJLHYMHEYDYDSQYCDDHGZUNDZCLZYZLLZNTNYZGSLHSLPJJBDGWXPCDUTJCKLKCLWKLLCASSTKZZDNQNTTLYYZSSYSSZZRYLJQKCQDHHCRXRZYDGRGCWCGZQFFFPPJFZYNAKRGYWYQPQXXFKJTSZZXSWZDDFBBXTBGTZKZNPZZPZXZPJSZBMQHKCYXYLDKLJNYPKYGHGDZJXXEAHPNZKZTZCMXCXMMJXNKSZQNMNLWBWWXJKYHCPSTMCSQTZJYXTPCTPDTNNPGLLLZSJLSPBLPLQHDTNJNLYYRSZFFJFQWDPHZDWMRZCCLODAXNSSNYZRESTYJWJYJDBCFXNMWTTBYLWSTSZGYBLJPXGLBOCLHPCBJLTMXZLJYLZXCLTPNCLCKXTPZJSWCYXSFYSZDKNTLBYJCYJLLSTGQCBXRYZXBXKLYLHZLQZLNZCXWJZLJZJNCJHXMNZZGJZZXTZJXYCYYCXXJYYXJJXSSSJSTSSTTPPGQTCSXWZDCSYFPTFBFHFBBLZJCLZZDBXGCXLQPXKFZFLSYLTUWBMQJHSZBMDDBCYSCCLDXYCDDQLYJJWMQLLCSGLJJSYFPYYCCYLTJANTJJPWYCMMGQYYSXDXQMZHSZXPFTWWZQSWQRFKJLZJQQYFBRXJHHFWJJZYQAZMYFRHCYYBYQWLPEXCCZSTYRLTTDMQLYKMBBGMYYJPRKZNPBSXYXBHYZDJDNGHPMFSGMWFZMFQMMBCMZZCJJLCNUXYQLMLRYGQZCYXZLWJGCJCGGMCJNFYZZJHYCPRRCMTZQZXHFQGTJXCCJEAQCRJYHPLQLSZDJRBCQHQDYRHYLYXJSYMHZYDWLDFRYHBPYDTSSCNWBXGLPZMLZZTQSSCPJMXXYCSJYTYCGHYCJWYRXXLFEMWJNMKLLSWTXHYYYNCMMCWJDQDJZGLLJWJRKHPZGGFLCCSCZMCBLTBHBQJXQDSPDJZZGKGLFQYWBZYZJLTSTDHQHCTCBCHFLQMPWDSHYYTQWCNZZJTLBYMBPDYYYXSQKXWYYFLXXNCWCXYPMAELYKKJMZZZBRXYYQJFLJPFHHHYTZZXSGQQMHSPGDZQWBWPJHZJDYSCQWZKTXXSQLZYYMYSDZGRXCKKUJLWPYSYSCSYZLRMLQSYLJXBCXTLWDQZPCYCYKPPPNSXFYZJJRCEMHSZMSXLXGLRWGCSTLRSXBZGBZGZTCPLUJLSLYLYMTXMTZPALZXPXJTJWTCYYZLBLXBZLQMYLXPGHDSLSSDMXMBDZZSXWHAMLCZCPJMCNHJYSNSYGCHSKQMZZQDLLKABLWJXSFMOCDXJRRLYQZKJMYBYQLYHETFJZFRFKSRYXFJTWDSXXSYSQJYSLYXWJHSNLXYYXHBHAWHHJZXWMYLJCSSLKYDZTXBZSYFDXGXZJKHSXXYBSSXDPYNZWRPTQZCZENYGCXQFJYKJBZMLJCMQQXUOXSLYXXLYLLJDZBTYMHPFSTTQQWLHOKYBLZZALZXQLHZWRRQHLSTMYPYXJJXMQSJFNBXYXYJXXYQYLTHYLQYFMLKLJTMLLHSZWKZHLJMLHLJKLJSTLQXYLMBHHLNLZXQJHXCFXXLHYHJJGBYZZKBXSCQDJQDSUJZYYHZHHMGSXCSYMXFEBCQWWRBPYYJQTYZCYQYQQZYHMWFFHGZFRJFCDPXNTQYZPDYKHJLFRZXPPXZDBBGZQSTLGDGYLCQMLCHHMFYWLZYXKJLYPQHSYWMQQGQZMLZJNSQXJQSYJYCBEHSXFSZPXZWFLLBCYYJDYTDTHWZSFJMQQYJLMQXXLLDTTKHHYBFPWTYYSQQWNQWLGWDEBZWCMYGCULKJXTMXMYJSXHYBRWFYMWFRXYQMXYSZTZZTFYKMLDHQDXWYYNLCRYJBLPSXCXYWLSPRRJWXHQYPHTYDNXHHMMYWYTZCSQMTSSCCDALWZTCPQPYJLLQZYJSWXMZZMMYLMXCLMXCZMXMZSQTZPPQQBLPGXQZHFLJJHYTJSRXWZXSCCDLXTYJDCQJXSLQYCLZXLZZXMXQRJMHRHZJBHMFLJLMLCLQNLDXZLLLPYPSYJYSXCQQDCMQJZZXHNPNXZMEKMXHYKYQLXSXTXJYYHWDCWDZHQYYBGYBCYSCFGPSJNZDYZZJZXRZRQJJYMCANYRJTLDPPYZBSTJKXXZYPFDWFGZZRPYMTNGXZQBYXNBUFNQKRJQZMJEGRZGYCLKXZDSKKNSXKCLJSPJYYZLQQJYBZSSQLLLKJXTBKTYLCCDDBLSPPFYLGYDTZJYQGGKQTTFZXBDKTYYHYBBFYTYYBCLPDYTGDHRYRNJSPTCSNYJQHKLLLZSLYDXXWBCJQSPXBPJZJCJDZFFXXBRMLAZHCSNDLBJDSZBLPRZTSWSBXBCLLXXLZDJZSJPYLYXXYFTFFFBHJJXGBYXJPMMMPSSJZJMTLYZJXSWXTYLEDQPJMYGQZJGDJLQJWJQLLSJGJGYGMSCLJJXDTYGJQJQJCJZCJGDZZSXQGSJGGCXHQXSNQLZZBXHSGZXCXYLJXYXYYDFQQJHJFXDHCTXJYRXYSQTJXYEFYYSSYYJXNCYZXFXMSYSZXYYSCHSHXZZZGZZZGFJDLTYLNPZGYJYZYYQZPBXQBDZTZCZYXXYHHSQXSHDHGQHJHGYWSZTMZMLHYXGEBTYLZKQWYTJZRCLEKYSTDBCYKQQSAYXCJXWWGSBHJYZYDHCSJKQCXSWXFLTYNYZPZCCZJQTZWJQDZZZQZLJJXLSBHPYXXPSXSHHEZTXFPTLQYZZXHYTXNCFZYYHXGNXMYWXTZSJPTHHGYMXMXQZXTSBCZYJYXXTYYZYPCQLMMSZMJZZLLZXGXZAAJZYXJMZXWDXZSXZDZXLEYJJZQBHZWZZZQTZPSXZTDSXJJJZNYAZPHXYYSRNQDTHZHYYKYJHDZXZLSWCLYBZYECWCYCRYLCXNHZYDZYDYJDFRJJHTRSQTXYXJRJHOJYNXELXSFSFJZGHPZSXZSZDZCQZBYYKLSGSJHCZSHDGQGXYZGXCHXZJWYQWGYHKSSEQZZNDZFKWYSSTCLZSTSYMCDHJXXYWEYXCZAYDMPXMDSXYBSQMJMZJMTZQLPJYQZCGQHXJHHLXXHLHDLDJQCLDWBSXFZZYYSCHTYTYYBHECXHYKGJPXHHYZJFXHWHBDZFYZBCAPNPGNYDMSXHMMMMAMYNBYJTMPXYYMCTHJBZYFCGTYHWPHFTWZZEZSBZEGPFMTSKFTYCMHFLLHGPZJXZJGZJYXZSBBQSCZZLZCCSTPGXMJSFTCCZJZDJXCYBZLFCJSYZFGSZLYBCWZZBYZDZYPSWYJZXZBDSYUXLZZBZFYGCZXBZHZFTPBGZGEJBSTGKDMFHYZZJHZLLZZGJQZLSFDJSSCBZGPDLFZFZSZYZYZSYGCXSNXXCHCZXTZZLJFZGQSQYXZJQDCCZTQCDXZJYQJQCHXZTDLGSCXZSYQJQTZWLQDQZTQCHQQJZYEZZZPBWKDJFCJPZTYPQYQTTYNLMBDKTJZPQZQZZFPZSBNJLGYJDXJDZZKZGQKXDLPZJTCJDQBXDJQJSTCKNXBXZMSLYJCQMTJQWWCJQNJNLLLHJCWQTBZQYDZCZPZZDZYDDCYZZZCCJTTJFZDPRRTZTJDCQTQZDTJNPLZBCLLCTZSXKJZQZPZLBZRBTJDCXFCZDBCCJJLTQQPLDCGZDBBZJCQDCJWYNLLZYZCCDWLLXWZLXRXNTQQCZXKQLSGDFQTDDGLRLAJJTKUYMKQLLTZYTDYYCZGJWYXDXFRSKSTQTENQMRKQZHHQKDLDAZFKYPBGGPZREBZZYKZZSPEGJXGYKQZZZSLYSYYYZWFQZYLZZLZHWCHKYPQGNPGBLPLRRJYXCCSYYHSFZFYBZYYTGZXYLXCZWXXZJZBLFFLGSKHYJZEYJHLPLLLLCZGXDRZELRHGKLZZYHZLYQSZZJZQLJZFLNBHGWLCZCFJYSPYXZLZLXGCCPZBLLCYBBBBUBBCBPCRNNZCZYRBFSRLDCGQYYQXYGMQZWTZYTYJXYFWTEHZZJYWLCCNTZYJJZDEDPZDZTSYQJHDYMBJNYJZLXTSSTPHNDJXXBYXQTZQDDTJTDYYTGWSCSZQFLSHLGLBCZPHDLYZJYCKWTYTYLBNYTSDSYCCTYSZYYEBHEXHQDTWNYGYCLXTSZYSTQMYGZAZCCSZZDSLZCLZRQXYYELJSBYMXSXZTEMBBLLYYLLYTDQYSHYMRQWKFKBFXNXSBYCHXBWJYHTQBPBSBWDZYLKGZSKYHXQZJXHXJXGNLJKZLYYCDXLFYFGHLJGJYBXQLYBXQPQGZTZPLNCYPXDJYQYDYMRBESJYYHKXXSTMXRCZZYWXYQYBMCLLYZHQYZWQXDBXBZWZMSLPDMYSKFMZKLZCYQYCZLQXFZZYDQZPZYGYJYZMZXDZFYFYTTQTZHGSPCZMLCCYTZXJCYTJMKSLPZHYSNZLLYTPZCTZZCKTXDHXXTQCYFKSMQCCYYAZHTJPCYLZLYJBJXTPNYLJYYNRXSYLMMNXJSMYBCSYSYLZYLXJJQYLDZLPQBFZZBLFNDXQKCZFYWHGQMRDSXYCYTXNQQJZYYPFZXDYZFPRXEJDGYQBXRCNFYYQPGHYJDYZXGRHTKYLNWDZNTSMPKLBTHBPYSZBZTJZSZZJTYYXZPHSSZZBZCZPTQFZMYFLYPYBBJQXZMXXDJMTSYSKKBJZXHJCKLPSMKYJZCXTMLJYXRZZQSLXXQPYZXMKYXXXJCLJPRMYYGADYSKQLSNDHYZKQXZYZTCGHZTLMLWZYBWSYCTBHJHJFCWZTXWYTKZLXQSHLYJZJXTMPLPYCGLTBZZTLZJCYJGDTCLKLPLLQPJMZPAPXYZLKKTKDZCZZBNZDYDYQZJYJGMCTXLTGXSZLMLHBGLKFWNWZHDXUHLFMKYSLGXDTWWFRJEJZTZHYDXYKSHWFZCQSHKTMQQHTZHYMJDJSKHXZJZBZZXYMPAGQMSTPXLSKLZYNWRTSQLSZBPSPSGZWYHTLKSSSWHZZLYYTNXJGMJSZSUFWNLSOZTXGXLSAMMLBWLDSZYLAKQCQCTMYCFJBSLXCLZZCLXXKSBZQCLHJPSQPLSXXCKSLNHPSFQQYTXYJZLQLDXZQJZDYYDJNZPTUZDSKJFSLJHYLZSQZLBTXYDGTQFDBYAZXDZHZJNHHQBYKNXJJQCZMLLJZKSPLDYCLBBLXKLELXJLBQYCXJXGCNLCQPLZLZYJTZLJGYZDZPLTQCSXFDMNYCXGBTJDCZNBGBQYQJWGKFHTNPYQZQGBKPBBYZMTJDYTBLSQMPSXTBNPDXKLEMYYCJYNZCTLDYKZZXDDXHQSHDGMZSJYCCTAYRZLPYLTLKXSLZCGGEXCLFXLKJRTLQJAQZNCMBYDKKCXGLCZJZXJHPTDJJMZQYKQSECQZDSHHADMLZFMMZBGNTJNNLGBYJBRBTMLBYJDZXLCJLPLDLPCQDHLXZLYCBLCXZZJADJLNZMMSSSMYBHBSQKBHRSXXJMXSDZNZPXLGBRHWGGFCXGMSKLLTSJYYCQLTSKYWYYHYWXBXQYWPYWYKQLSQPTNTKHQCWDQKTWPXXHCPTHTWUMSSYHBWCRWXHJMKMZNGWTMLKFGHKJYLSYYCXWHYECLQHKQHTTQKHFZLDXQWYZYYDESBPKYRZPJFYYZJCEQDZZDLATZBBFJLLCXDLMJSSXEGYGSJQXCWBXSSZPDYZCXDNYXPPZYDLYJCZPLTXLSXYZYRXCYYYDYLWWNZSAHJSYQYHGYWWAXTJZDAXYSRLTDPSSYYFNEJDXYZHLXLLLZQZSJNYQYQQXYJGHZGZCYJCHZLYCDSHWSHJZYJXCLLNXZJJYYXNFXMWFPYLCYLLABWDDHWDXJMCXZTZPMLQZHSFHZYNZTLLDYWLSLXHYMMYLMBWWKYXYADTXYLLDJPYBPWUXJMWMLLSAFDLLYFLBHHHBQQLTZJCQJLDJTFFKMMMBYTHYGDCQRDDWRQJXNBYSNWZDBYYTBJHPYBYTTJXAAHGQDQTMYSTQXKBTZPKJLZRBEQQSSMJJBDJOTGTBXPGBKTLHQXJJJCTHXQDWJLWRFWQGWSHCKRYSWGFTGYGBXSDWDWRFHWYTJJXXXJYZYSLPYYYPAYXHYDQKXSHXYXGSKQHYWFDDDPPLCJLQQEEWXKSYYKDYPLTJTHKJLTCYYHHJTTPLTZZCDLTHQKZXQYSTEEYWYYZYXXYYSTTJKLLPZMCYHQGXYHSRMBXPLLNQYDQHXSXXWGDQBSHYLLPJJJTHYJKYPPTHYYKTYEZYENMDSHLCRPQFDGFXZPSFTLJXXJBSWYYSKSFLXLPPLBBBLBSFXFYZBSJSSYLPBBFFFFSSCJDSTZSXZRYYSYFFSYZYZBJTBCTSBSDHRTJJBYTCXYJEYLXCBNEBJDSYXYKGSJZBXBYTFZWGENYHHTHZHHXFWGCSTBGXKLSXYWMTMBYXJSTZSCDYQRCYTWXZFHMYMCXLZNSDJTTTXRYCFYJSBSDYERXJLJXBBDEYNJGHXGCKGSCYMBLXJMSZNSKGXFBNBPTHFJAAFXYXFPXMYPQDTZCXZZPXRSYWZDLYBBKTYQPQJPZYPZJZNJPZJLZZFYSBTTSLMPTZRTDXQSJEHBZYLZDHLJSQMLHTXTJECXSLZZSPKTLZKQQYFSYGYWPCPQFHQHYTQXZKRSGTTSQCZLPTXCDYYZXSQZSLXLZMYCPCQBZYXHBSXLZDLTCDXTYLZJYYZPZYZLTXJSJXHLPMYTXCQRBLZSSFJZZTNJYTXMYJHLHPPLCYXQJQQKZZSCPZKSWALQSBLCCZJSXGWWWYGYKTJBBZTDKHXHKGTGPBKQYSLPXPJCKBMLLXDZSTBKLGGQKQLSBKKTFXRMDKBFTPZFRTBBRFERQGXYJPZSSTLBZTPSZQZSJDHLJQLZBPMSMMSXLQQNHKNBLRDDNXXDHDDJCYYGYLXGZLXSYGMQQGKHBPMXYXLYTQWLWGCPBMQXCYZYDRJBHTDJYHQSHTMJSBYPLWHLZFFNYPMHXXHPLTBQPFBJWQDBYGPNZTPFZJGSDDTQSHZEAWZZYLLTYYBWJKXXGHLFKXDJTMSZSQYNZGGSWQSPHTLSSKMCLZXYSZQZXNCJDQGZDLFNYKLJCJLLZLMZZNHYDSSHTHZZLZZBBHQZWWYCRZHLYQQJBEYFXXXWHSRXWQHWPSLMSSKZTTYGYQQWRSLALHMJTQJSMXQBJJZJXZYZKXBYQXBJXSHZTSFJLXMXZXFGHKZSZGGYLCLSARJYHSLLLMZXELGLXYDJYTLFBHBPNLYZFBBHPTGJKWETZHKJJXZXXGLLJLSTGSHJJYQLQZFKCGNNDJSSZFDBCTWWSEQFHQJBSAQTGYPQLBXBMMYWXGSLZHGLZGQYFLZBYFZJFRYSFMBYZHQGFWZSYFYJJPHZBYYZFFWODGRLMFTWLBZGYCQXCDJYGZYYYYTYTYDWEGAZYHXJLZYYHLRMGRXXZCLHNELJJTJTPWJYBJJBXJJTJTEEKHWSLJPLPSFYZPQQBDLQJJTYYQLYZKDKSQJYYQZLDQTGJQYZJSUCMRYQTHTEJMFCTYHYPKMHYZWJDQFHYYXWSHCTXRLJHQXHCCYYYJLTKTTYTMXGTCJTZAYYOCZLYLBSZYWJYTSJYHBYSHFJLYGJXXTMZYYLTXXYPZLXYJZYZYYPNHMYMDYYLBLHLSYYQQLLNJJYMSOYQBZGDLYXYLCQYXTSZEGXHZGLHWBLJHEYXTWQMAKBPQCGYSHHEGQCMWYYWLJYJHYYZLLJJYLHZYHMGSLJLJXCJJYCLYCJPCPZJZJMMYLCQLNQLJQJSXYJMLSZLJQLYCMMHCFMMFPQQMFYLQMCFFQMMMMHMZNFHHJGTTHHKHSLNCHHYQDXTMMQDCYZYXYQMYQYLTDCYYYZAZZCYMZYDLZFFFMMYCQZWZZMABTBYZTDMNZZGGDFTYPCGQYTTSSFFWFDTZQSSYSTWXJHXYTSXXYLBYQHWWKXHZXWZNNZZJZJJQJCCCHYYXBZXZCYZTLLCQXYNJYCYYCYNZZQYYYEWYCZDCJYCCHYJLBTZYYCQWMPWPYMLGKDLDLGKQQBGYCHJXY";
export function GetFirstPinYinLetter(unicodeStr) {
    if (!unicodeStr || /^ +$/g.test(unicodeStr))
        return "";
    var result = [];
    for (var i = 0; i < unicodeStr.length; i++) {
        var unicode = unicodeStr.charCodeAt(i);
        var ch = unicodeStr.charAt(i);
        if (unicode >= 19968 && unicode <= 40869) {
            ch = pinyinFirstLetterDict.charAt(unicode - 19968);
            ch = pinyinFirstLetterDictPolyphone[unicode] || ch;
        }
        else {
            ch = ch.toUpperCase();
        }
        result.push(ch);
    }
    return JSON.stringify(result);
}
