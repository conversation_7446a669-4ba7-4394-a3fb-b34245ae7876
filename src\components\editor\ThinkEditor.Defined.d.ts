import { ThinkEditor } from "./ThinkEditor";
export declare class BitItem {
    name: string;
    fieldWidth: number;
    valueMask: number;
    mask: number;
    shift: number;
    constructor(name: string, fieldWidth: number);
}
export declare class BitFields {
    all: number;
    private bitItems;
    constructor(bitItems: BitItem[]);
    getBitField(bitItem: BitItem): number;
    setBitField(bitItem: BitItem, val: number): void;
    get cfg(): string;
}
export declare class SeparatorCfg extends BitFields {
    constructor();
}
export declare class QCCfg extends BitFields {
    constructor();
}
export declare class SearchReplaceCfg extends BitFields {
    constructor();
}
export declare class ElementCfg extends BitFields {
    constructor();
}
export declare class CheckBoxCfg extends BitFields {
    constructor();
}
export declare class BarCodeCfg extends BitFields {
    constructor();
}
export declare class AttachCfg extends BitFields {
    constructor();
}
export declare class EditBehaviorCfg extends BitFields {
    constructor();
}
export declare enum E_PRINT_SCOPE_MODE {
    All = 0,
    OddNumbers = 1,
    EvenNumbers = 2,
    PagesRange = 3
}
export declare enum E_SYSTEM_AUTH_MODE {
    Demonstration = 0,
    Register = 1
}
export declare enum E_MESSAGE_LEVEL {
    Error = 0,
    Warn = 1,
    Notice = 2
}
export declare enum E_QC_CLASS {
    String = 0,
    Number = 1,
    DateTime = 2,
    RegularExpression = 3
}
export declare enum E_SPLIT_PARAGRAPH_MODE {
    FollowParent = 0,
    BreakLine0SplitParagraph0 = 1,
    BreakLine1SplitParagraph0 = 2,
    BreakLine1SplitParagraph1 = 3
}
export declare enum E_LINE_SPACE_RULE {
    LineSpaceSingle = 0,
    LineSpace1pt5 = 1,
    LineSpaceDouble = 2,
    LineSpaceAtLeast = 3,
    LineSpaceExactly = 4,
    LineSpaceMultiple = 5
}
export declare enum E_LIST_LEVEL {
    Normal = 0,
    Level_1 = 1,
    Level_2 = 2,
    Level_3 = 3,
    Level_4 = 4,
    Level_5 = 5,
    Level_6 = 6,
    Level_7 = 7,
    Level_8 = 8,
    Level_9 = 9
}
export declare enum E_SPLIT_PARAGRAH_MODE {
    FollowParent = 0,
    BreakLine0SplitParagrah0 = 1,
    BreakLine1SplitParagrah0 = 2,
    BreakLine1SplitParagrah1 = 3
}
export declare enum E_ATTACH_LAYER {
    Lower = 0,
    Upper = 1
}
export declare enum E_ATTACH_TYPE {
    Background = 0,
    WaterMark = 1,
    Lines = 2,
    Rectangle = 3,
    Polygon = 4,
    Ellipse = 5,
    LinkBox = 6,
    Magnifier = 7
}
export declare enum E_ATTACH_SOURCE {
    User = 0,
    SystemAuth = 1,
    SystemDynamic = 2
}
export declare enum E_TEXTURE_TYPE {
    Null = 0,
    Type1 = 1,
    Type2 = 2,
    Type3 = 3,
    Type4 = 4,
    Type5 = 5,
    Type6 = 6,
    Type7 = 7,
    Type8 = 8,
    Type9 = 9,
    Type10 = 10,
    Type11 = 11,
    Type12 = 12,
    Type13 = 13,
    Type14 = 14
}
export declare enum E_WATER_MARK_TYPE {
    Text = 0,
    Image = 1
}
export declare enum E_BORDER_STYLE {
    NULL = 0,
    SOLID = 1
}
export declare enum E_PAGE_NUM_SCOPE {
    WholeDocnment = 0,
    InnerSection = 1
}
export declare enum E_PAGE_NUM_STYLE {
    OrderListModel_0 = 0,
    OrderListModel_1 = 1,
    OrderListModel_2 = 2,
    OrderListModel_3 = 3,
    OrderListModel_4 = 4,
    OrderListModel_5 = 5,
    OrderListModel_6 = 6,
    OrderListModel_7 = 7,
    OrderListModel_8 = 8,
    OrderListModel_9 = 9
}
export declare enum E_PAGE_NUM_FORMAT {
    PageOrder = 0,
    PagesCount = 1,
    PageOrderFormat = 2,
    PagesCountFormat = 3,
    PageOrderPagesCountFormat = 4
}
export declare enum E_CHECK_FIGURE_STYLE {
    Style_0 = 0,
    Style_1 = 1,
    Style_2 = 2,
    Style_3 = 3,
    Style_4 = 4
}
export declare enum E_LOG_LEVEL {
    Error = 0,
    Warn = 1,
    Info = 2,
    Debug = 3,
    All = 4
}
export declare enum E_BARCODE_TYPE {
    CODE11 = 1,
    C25MATRIX = 2,
    C25INTER = 3,
    C25IATA = 4,
    C25LOGIC = 5,
    C25IND = 6,
    CODE39 = 7,
    EXCODE39 = 8,
    EANX = 13,
    EAN128 = 16,
    CODABAR = 18,
    CODE128 = 20,
    DPLEIT = 21,
    DPIDENT = 22,
    CODE16K = 23,
    CODE49 = 24,
    CODE93 = 25,
    FLAT = 28,
    RSS14 = 29,
    RSS_LTD = 30,
    RSS_EXP = 31,
    TELEPEN = 32,
    UPCA = 34,
    UPCE = 37,
    POSTNET = 40,
    MSI_PLESSEY = 47,
    FIM = 49,
    LOGMARS = 50,
    PHARMA = 51,
    PZN = 52,
    PHARMA_TWO = 53,
    PDF417 = 55,
    PDF417TRUNC = 56,
    MAXICODE = 57,
    QRCODE = 58,
    CODE128B = 60,
    AUSPOST = 63,
    AUSREPLY = 66,
    AUSROUTE = 67,
    AUSREDIRECT = 68,
    ISBNX = 69,
    RM4SCC = 70,
    DATAMATRIX = 71,
    EAN14 = 72,
    CODABLOCKF = 74,
    NVE18 = 75,
    JAPANPOST = 76,
    KOREAPOST = 77,
    RSS14STACK = 79,
    RSS14STACK_OMNI = 80,
    RSS_EXPSTACK = 81,
    PLANET = 82,
    MICROPDF417 = 84,
    ONECODE = 85,
    PLESSEY = 86,
    TELEPEN_NUM = 87,
    ITF14 = 89,
    KIX = 90,
    AZTEC = 92,
    DAFT = 93,
    MICROQR = 97,
    HIBC_128 = 98,
    HIBC_39 = 99,
    HIBC_DM = 102,
    HIBC_QR = 104,
    HIBC_PDF = 106,
    HIBC_MICPDF = 108,
    HIBC_BLOCKF = 110,
    HIBC_AZTEC = 112,
    AZRUNE = 128,
    CODE32 = 129,
    EANX_CC = 130,
    EAN128_CC = 131,
    RSS14_CC = 132,
    RSS_LTD_CC = 133,
    RSS_EXP_CC = 134,
    UPCA_CC = 135,
    UPCE_CC = 136,
    RSS14STACK_CC = 137,
    RSS14_OMNI_CC = 138,
    RSS_EXPSTACK_CC = 139,
    CHANNEL = 140,
    CODEONE = 141,
    GRIDMATRIX = 142
}
export declare enum E_FORMULA_STYLE {
    Common = 0,
    MensesStyle_1 = 1,
    MensesStyle_2 = 2,
    MensesStyle_3 = 3,
    MensesStyle_4 = 4,
    PupilPosition = 5,
    LightProjection = 6,
    FetalHeart = 7,
    PermanentTeethPos = 8,
    DeciduousTeethPos = 9
}
export declare enum E_TIME_TYPE {
    Date = 0,
    DateTime = 1,
    DateTimeNoSecond = 2,
    Time = 3,
    TimeNoSecond = 4
}
export declare enum E_INPUT_MODE {
    InputText = 0,
    InputNumber = 1,
    SelectOption = 2,
    SelectDateTime = 3
}
export declare enum E_UPDATE_PARAGRAPH_CFG_MODE {
    ForEmptyContent = 0,
    ForEmptyContentAndIdentity = 1,
    ForceUpdate = 2,
    ForceNoUpdate = 3
}
export declare enum E_IDENTITY_OBJECT_TYPE {
    Unknown = 0,
    Paragraph = 1,
    Element = 2,
    Image = 3,
    Table = 4,
    CheckBox = 5,
    Formula = 6
}
export declare enum E_DOC_FORMAT {
    Unknown = 0,
    TEXT = 1,
    XML = 2,
    HTML = 3,
    Content = 4,
    TextEx = 5
}
export declare enum E_DOC_TYPE {
    Template = 0,
    Entity = 1,
    Fragment = 2,
    Knowledge = 3
}
export declare enum E_DOCS_ORGANIZE_MODE {
    Single = 0,
    MergeContent = 1,
    UnionContent = 2,
    UnionSection = 3
}
export declare enum E_PAGES_LAYOUT_MODE {
    SinglePage = 0,
    Ratio = 1,
    MultiPages = 2,
    FillViewWidth = 3
}
export declare enum E_VIEW_MODE {
    Edit = 0,
    Browse = 1,
    Print = 2,
    SelectPagePrint = 3,
    SelectContentPrint = 4,
    ContinuePrint = 5
}
export declare enum E_IMAGE_TYPE {
    ERROR = 0,
    ICO = 1,
    CUR = 2,
    BMP = 3,
    GIF = 4,
    JPG = 5,
    LBM = 6,
    PCX = 7,
    PNG = 8,
    PNM = 9,
    SVG = 10,
    TIF = 11,
    XCF = 12,
    XPM = 13,
    XV = 14,
    WEBP = 15
}
export declare enum E_DISPLAY_MODE {
    Hide = 0,
    Show = 1
}
export declare enum E_SET_MODE {
    InsertCommon = 0,
    ModifyCommon = 1,
    InsertSpecial = 2,
    ModifySpecial = 3
}
export declare enum E_BRUSH_STATE {
    BrushNull = 0,
    BrushOnce = 1,
    BrushHold = 2
}
export declare enum E_PASTE_TYPE {
    Normal = 0,
    OnlyText = 1,
    OnlyXml = 2
}
export declare enum E_PASTE_BEHAVIOR_MODE {
    NoCheck = 0,
    ForbidNeedNotify = 1,
    ForbidNoNotify = 2,
    UserChoose = 3
}
export declare enum E_SCRIPT_MODE {
    Normal = 0,
    SuperScript = 1,
    SubScript = 2
}
export declare enum E_FERRULE_MODE {
    Normal = 0,
    Circle = 1,
    Rectangle = 2
}
export declare enum E_SHOW_BG_MODE {
    AlwaysHide = 0,
    AlwaysShow = 1,
    ShowBySelected = 2
}
export declare enum E_LIST_MODE {
    Normal = 0,
    SymbolListModel_0 = 1,
    SymbolListModel_1 = 2,
    SymbolListModel_2 = 3,
    SymbolListModel_3 = 4,
    SymbolListModel_4 = 5,
    SymbolListModel_5 = 6,
    SymbolListModel_6 = 7,
    SymbolListModel_7 = 8,
    SymbolListModel_8 = 9,
    SymbolListModel_9 = 10,
    OrderListModel_0 = 11,
    OrderListModel_1 = 12,
    OrderListModel_2 = 13,
    OrderListModel_3 = 14,
    OrderListModel_4 = 15,
    OrderListModel_5 = 16,
    OrderListModel_6 = 17,
    OrderListModel_7 = 18,
    OrderListModel_8 = 19,
    OrderListModel_9 = 20
}
export declare enum E_COMBINE_STYLE {
    Common = 0,
    Style1 = 1
}
export declare enum E_SPECIFIC_INDENT_FORMAT {
    NoIndent = 0,
    FristLine = 1,
    Hanging = 2
}
export declare enum E_ALIGN_HORIZONTAL_MODE {
    Left = 0,
    Center = 1,
    Right = 2,
    Justify = 3,
    Distribute = 4
}
export declare enum E_ALIGN_VERTICAL_MODE {
    Top = 0,
    Middle = 1,
    Bottom = 2
}
export declare enum E_ALIGN_VERTICAL_LAYOUT {
    Bottom = 0,
    SelfMiddle = 1,
    OtherMiddle = 2,
    TOP = 3
}
export declare enum E_LAYOUT_VERTICAL_ALIGN {
    Bottom = 0,
    SelfMiddle = 1,
    OtherMiddle = 2,
    TOP = 3
}
export declare enum E_TABLE_INSERT_MODE {
    ColLeft = 0,
    ColRight = 1,
    RowUp = 2,
    RowDown = 3
}
export declare enum E_TABLE_DELETE_MODE {
    Col = 0,
    Row = 1,
    Table = 2
}
export declare enum E_CELL_OPT_MODE {
    Merge = 0,
    Split = 1
}
export declare enum E_ALGO_ROLE {
    In = 0,
    Out = 1
}
export declare enum E_ALGO_MODE {
    Common = 0,
    Addition = 1
}
export declare enum E_ALGO_SCOPE {
    Doc = 0,
    Table = 1,
    Row = 2
}
export declare enum E_MOVE_FOCUS_HOTKEY {
    Default = 0,
    None = 1,
    Tab = 2,
    Enter = 3
}
export declare enum E_TRIGGER_EVENT {
    ValueChange = 0,
    Click = 1,
    DbClick = 2
}
export declare enum E_EXECUTE_MODE {
    Always = 0,
    Once = 1,
    Reset = 2
}
export declare enum E_EVENT_HANDLE {
    event = "event",
    action = "action",
    request = "request"
}
export declare enum E_EVENT_TYPE {
    mouseMove = "mouseMove",
    mouseOut = "mouseOut",
    mouseOver = "mouseOver",
    mouseClick = "mouseClick",
    mouseDown = "mouseDown",
    mouseUp = "mouseUp",
    mouseWheel = "mouseWheel",
    touchStart = "touchStart",
    keyDown = "keyDown",
    keyUp = "keyUp",
    documentKeyDown = "documentKeyDown",
    documentKeyUp = "documentKeyUp",
    documentMouseMove = "documentMouseMove",
    documentMouseUp = "documentMouseUp"
}
export declare enum E_INSTANCE_EVENT_KEY {
    createdDocEditor = "createdDocEditor",
    removedDocEditor = "removedDocEditor",
    editorInstanceFocusChange = "editorInstanceFocusChange",
    createdThinkEditorView = "createdThinkEditorView",
    destroyThinkEditorView = "destroyThinkEditorView"
}
export declare enum E_EVENT_KEY {
    drop = "drop",
    requestSources = "requestSources",
    setSources = "setSources",
    setOptions = "setOptions",
    delOptions = "delOptions",
    setAlgorithms = "setAlgorithms",
    optionResource = "optionResource",
    elementContentChange = "elementContentChange",
    timeStamp = "timeStamp",
    focusChange = "focusChange",
    buttonPressed = "buttonPressed",
    setSearchHistory = "setSearchHistory",
    setReplaceHistory = "setReplaceHistory",
    setSpecialSymbolHistory = "setSpecialSymbolHistory",
    setCursorStyle = "setCursorStyle",
    fontFormat = "fontFormat",
    paragraphStyle = "paragraphStyle",
    instanceChange = "instanceChange",
    editorFocus = "editorFocus",
    requestFont = "requestFont",
    fontsName = "fontsName",
    textEditFocused = "textEditFocused",
    docsViewChange = "docsViewChange",
    preLoadFontData = "preLoadFontData",
    docModified = "docModified",
    setElementContent = "setElementContent",
    imageResource = "imageResource",
    closeDoc = "closeDoc",
    parseDoc = "parseDoc",
    loaded = "loaded",
    saveDoc = "saveDoc",
    visibleDocChange = "visibleDocChange",
    algorithmCalc = "algorithmCalc",
    selectRangeChange = "selectRangeChange",
    user_defined1 = "user_defined1",
    browserWebglContextLost = "browserWebglContextLost",
    printDoc = "printDoc",
    annotateFocusChange = "annotateFocusChange",
    setColor = "setColor",
    openMenu = "openMenu",
    closeMenu = "closeMenu",
    beginDownloadFontTime = "beginDownloadFontTime",
    endDownloadFontTime = "endDownloadFontTime",
    beginParseTime = "beginParseTime",
    endParseTime = "endParseTime",
    beginProduceLines = "beginProduceLines",
    endProduceLines = "endProduceLines",
    beginProducePages = "beginProducePages",
    endProducePages = "endProducePages",
    beginRequestFont = "beginRequestFont",
    endRequestFont = "endRequestFont",
    beginParseFont = "beginParseFont",
    endParseFont = "endParseFont",
    openFontSetWindow = "openFontSetWindow",
    closeFontSetWindow = "closeFontSetWindow",
    openParagraphSetWindow = "openParagraphSetWindow",
    closeParagraphSetWindow = "closeParagraphSetWindow",
    openInputHandleSelector = "openInputHandleSelector",
    closeInputHandleSelector = "closeInputHandleSelector",
    openInfoTips = "openInfoTips",
    closeInfoTips = "closeInfoTips",
    openFormulaSetWindow = "openFormulaSetWindow",
    openToothPositionSelector = "openToothPositionSelector",
    openBarCodeSetWindow = "openBarCodeSetWindow",
    openCheckBoxSetWindow = "openCheckBoxSetWindow",
    openPageNumSetWindow = "openPageNumSetWindow",
    openMessageBoxWindow = "openMessageBoxWindow",
    closeMessageBoxWindow = "closeMessageBoxWindow",
    openSearchReplaceWindow = "openSearchReplaceWindow",
    openKnowledgeSetWindow = "openKnowledgeSetWindow",
    openImageSetWindow = "openImageSetWindow",
    openPageSetWindow = "openPageSetWindow",
    closePageSetWindow = "closePageSetWindow",
    openElementSetWindow = "openElementSetWindow",
    closeElementSetWindow = "closeElementSetWindow",
    openTableSetWindow = "openTableSetWindow"
}
export declare class ThinkEditorEvent extends Event {
    private readonly _data;
    get data(): ThinkEditorEventData;
    constructor(type: string, data: ThinkEditorEventData);
}
export declare class ThinkEditorEventData {
    editor?: ThinkEditor;
    instance?: any;
    handle: E_EVENT_HANDLE;
    type: string;
    data: any;
    constructor(handle: E_EVENT_HANDLE, type: string, data: any, editor?: ThinkEditor);
    get editorId(): any;
}
declare class SymbolItem {
    content: string;
    fontName: string;
    constructor(content: string, fontName: string);
}
declare class EditorClipboard {
    xmlStr: string;
    textExStr: string;
    htmlStr: string;
    textStr: string;
}
export declare class ShareDataManager {
    clipboard: EditorClipboard;
    private searchHistory;
    private replaceHistory;
    private specialSymbolHistory;
    platform: string;
    constructor();
    SetSearchHistory(searchHistory: Array<string>): void;
    GetSearchHistory(): string[];
    SetReplaceHistory(replaceHistory: Array<string>): void;
    GetReplaceHistory(): string[];
    SetSpecialSymbolHistory(history: Array<SymbolItem>): void;
    GetSpecialSymbolHistory(): SymbolItem[];
}
export declare class Source {
    sourceClass: string;
    sourceId: string;
    content?: string;
    constructor(sourceClass: string, sourceId: string, content?: string);
}
export declare class SourceItem {
    sourceId: string;
    content?: string;
    constructor(sourceId: string, content?: string);
}
export declare class SourceClass {
    sourceClass: string;
    sourceItems: Array<SourceItem>;
    constructor(sourceClass: string);
    GetSourceItem(sourceId: string): SourceItem | undefined;
}
export declare class SourcesManager {
    sources: Array<SourceClass>;
    AddSource(sourceClass: string, sourceId: string, content?: string): void;
    AddSources(sources: Array<Source>): void;
    GetSrouceItem(sourceClass: string, sourceId: string): SourceItem | undefined;
    GetSource(sourceClass: string, sourceId: string): Source | undefined;
    GetAllSources(): Source[];
    GetSrouceClass(sourceClass: string): SourceClass | undefined;
}
export declare class OptionItem {
    constructor(id: string, group: string, weight: number, content: string);
    id: string;
    group: string;
    weight: number;
    content: string;
}
export declare class Option {
    optionClass: string;
    optionId: string;
    multipleChoice: boolean;
    groupExclusion: boolean;
    items: Array<OptionItem>;
    constructor(optionClass: string, optionId: string, multipleChoice?: boolean, groupExclusion?: boolean);
    RemoveInvalidData(): void;
}
export declare class OptionClass {
    optionClass: string;
    options: Array<Option>;
    constructor(optionClass: string);
    GetOption(optionIdStr: string): Option | undefined;
    AddOption(optionId: Option): void;
    DeleteOption(optionIdStr: string): boolean;
    RemoveInvalidData(): void;
}
export declare class OptionsManager {
    options: Array<OptionClass>;
    AddOptions(options: Array<Option>, forceUpdate?: boolean): void;
    AddOption(optionClass: string, optionId: string, items: Array<OptionItem>, forceUpdate?: boolean): void;
    GetOptionClass(optionClass: string): OptionClass | undefined;
    GetOption(optionClass: string, optionId: string): Option | undefined;
    DeleteOption(optionClass: string, optionId: string): boolean;
    GetAllOptions(): {
        optionClass: string;
        optionId: string;
        multipleChoice: boolean;
        groupExclusion: boolean;
    }[];
    RemoveInvalidData(): void;
}
export declare class Algorithm {
    algoGroup: string;
    algoMode: E_ALGO_MODE;
    algoScope: E_ALGO_SCOPE;
    constructor(algoGroup: string, algoMode: E_ALGO_MODE, algoScope: E_ALGO_SCOPE);
}
export declare class AlgorithmsManager {
    algorithms: Array<Algorithm>;
    AddAlgorithms(algorithms: Array<Algorithm>): void;
    AddAlgorithm(algoGroup: string, algoMode: E_ALGO_MODE, algoScope: E_ALGO_SCOPE): void;
    DelAlgorithm(algoGroup: string): boolean;
    GetAlgorithm(algoGroup: string): Algorithm | undefined;
    RemoveInvalidData(): void;
}
export declare class Expression {
    event: E_TRIGGER_EVENT;
    mode: E_EXECUTE_MODE;
    action: string;
    constructor(event: E_TRIGGER_EVENT, mode: E_EXECUTE_MODE, action: string);
}
export declare class ExpressionsManager {
    expressions: Array<Expression>;
    AddExpressions(expressions: Array<Expression>): void;
    AddExpression(event: E_TRIGGER_EVENT, mode: E_EXECUTE_MODE, action: string): void;
    GetExpression(event: E_TRIGGER_EVENT, mode: E_EXECUTE_MODE, action: string): Expression | undefined;
    RemoveInvalidData(): void;
}
export declare class ThinkEditorLogger {
    private logLevel?;
    InitLogLevel(logLevel?: E_LOG_LEVEL): void;
    NeedLogError(): boolean;
    NeedLogWarn(): boolean;
    NeedLogInfo(): boolean;
    NeedLogDebug(): boolean;
    NeedLogEvent(): boolean;
    LogError(...data: any[]): void;
    LogWarn(...data: any[]): void;
    LogInfo(...data: any[]): void;
    LogDebug(...data: any): void;
    LogEvent(...data: any[]): void;
}
export declare class FontsManager {
    fontsDataMap: Map<string, ArrayBuffer>;
    fontsNameList: string[];
    localFontDatas: Array<any>;
    debugFont?: boolean;
    forbidLocalFonts?: boolean;
    AddFont(fullName: string, fontData: ArrayBuffer): void;
    GetFontData(fullName: string): Promise<any>;
    IsForbidLocalFonts(): boolean;
    LoadLocalFonts(): Promise<boolean>;
    LetUserAuthLocalFontAccessPermission(): Promise<any[]>;
    GetfontsNameList(): Promise<any>;
}
declare const editorLogger: ThinkEditorLogger;
declare const fontsManager: FontsManager;
declare const optionsManager: OptionsManager;
declare const algorithmsManager: AlgorithmsManager;
declare const shareDataManager: ShareDataManager;
export { fontsManager, optionsManager, algorithmsManager, shareDataManager, editorLogger };
//# sourceMappingURL=ThinkEditor.Defined.d.ts.map