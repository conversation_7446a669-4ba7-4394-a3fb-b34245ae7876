import { editor<PERSON>ogger, E_EVENT_KEY, E_EVENT_HANDLE, ThinkEditorEvent, ThinkEditorEventData } from "./ThinkEditor.Defined.js";
export class ThinkEditorInstance extends EventTarget {
    constructor(instanceId) {
        super();
        this.editors = new Array();
        this.instanceId = instanceId;
    }
    AddEditor(thinkEditor) {
        if (this.GetEditor(thinkEditor.docName) !== undefined) {
            editorLogger.LogWarn("无法添加相同docName的文档，请在外部UnInit该ThinkEditor！");
            return false;
        }
        if (this.editors.length == 0) {
            thinkEditor.selected = true;
        }
        this.editors.push(thinkEditor);
        this.addInnerEventListenerAgent(thinkEditor);
        this.CreateInstanceChangeEvent();
        return true;
    }
    SelectEditorByEditorId(editorId) {
        let editor = this.GetEditorByEditorId(editorId);
        if (editor === undefined) {
            return false;
        }
        return this.SelectEditor(editor.docName);
    }
    SelectEditor(docName) {
        if (this.GetEditor(docName) === undefined) {
            console.warn("SelectDoc失败：文档：", docName, " 在本实例中不存在！");
            return false;
        }
        let currentThinkEditor = this.GetSelectedEditor();
        if (currentThinkEditor != undefined) {
            currentThinkEditor.UnLoad();
        }
        let selectEditor;
        for (var i = 0; i < this.editors.length; i++) {
            if (this.editors[i].docName == docName) {
                this.editors[i].selected = true;
                selectEditor = this.editors[i];
            }
            else {
                this.editors[i].selected = false;
            }
        }
        if (selectEditor === undefined) {
            editorLogger.LogWarn("SelectDoc失败：selectDocInfo is undefined");
            return false;
        }
        if (selectEditor === undefined) {
            editorLogger.LogWarn("SelectDoc失败：thinkEditor is undefined");
            return false;
        }
        selectEditor.SelectDoc(docName);
        selectEditor.Load(selectEditor.container);
        selectEditor.selectedTime = new Date();
        this.CreateEditorFocusEvent(selectEditor);
        return true;
    }
    CreateInstanceChangeEvent() {
        var eventData = new ThinkEditorEventData(E_EVENT_HANDLE.event, E_EVENT_KEY.instanceChange, {});
        this.dispatchEditorsInstanceEvent(new ThinkEditorEvent(E_EVENT_KEY.instanceChange, eventData));
    }
    CreateEditorFocusEvent(editor) {
        var eventData = new ThinkEditorEventData(E_EVENT_HANDLE.event, E_EVENT_KEY.focusChange, {});
        eventData.editor = editor;
        this.dispatchEditorsInstanceEvent(new ThinkEditorEvent(E_EVENT_KEY.editorFocus, eventData));
    }
    dispatchEditorsInstanceEvent(e) {
        e.data.instance = this;
        return this.dispatchEvent(new ThinkEditorEvent(e.type, e.data));
    }
    GetLastEditor() {
        let editor;
        for (var i = 0; i < this.editors.length; i++) {
            if (editor === undefined) {
                editor = this.editors[i];
            }
            if (this.editors[i].selectedTime > editor.selectedTime) {
                editor = this.editors[i];
            }
        }
        return editor;
    }
    CloseEditor(docName) {
        let closeEditor = this.GetEditor(docName);
        if (closeEditor === undefined) {
            editorLogger.LogWarn("需要关闭的文档不存在：" + docName);
            return false;
        }
        let ret = this.CloseDocEditorHandle(closeEditor);
        this.CreateInstanceChangeEvent();
        return ret;
    }
    CloseAllEditors() {
        while (this.editors.length > 0) {
            this.CloseDocEditorHandle(this.editors[0]);
        }
        this.CreateInstanceChangeEvent();
    }
    CloseDocEditorHandle(closeThinkEditor) {
        this.removeInnerEventListenerAgent(closeThinkEditor);
        let docName = closeThinkEditor.docName;
        editorLogger.LogInfo("关闭文档：" + docName);
        if (closeThinkEditor !== undefined) {
            closeThinkEditor.UnInit();
        }
        this.editors.splice(this.editors.findIndex((x) => x.docName == docName), 1);
        let lastEditor = this.GetLastEditor();
        if (lastEditor === undefined) {
            return false;
        }
        this.SelectEditor(lastEditor.docName);
        return true;
    }
    GetEditorByShowName(showName) {
        return this.editors.find((x) => x.showName == showName);
    }
    GetEditor(docName) {
        return this.editors.find((x) => x.docName == docName);
    }
    GetEditorByEditorId(editorId) {
        return this.editors.find((x) => (x === null || x === void 0 ? void 0 : x.editorId) == editorId);
    }
    GetSelectedEditor() {
        return this.editors.find((x) => x.selected);
    }
    GetSelectedDocName() {
        let selectedEditor = this.GetSelectedEditor();
        return selectedEditor ? selectedEditor.docName : "";
    }
    GetEditorCount() {
        return this.editors.length;
    }
    addInnerEventListenerAgent(editor) {
        for (let keyEvent in E_EVENT_KEY) {
            editor.addEventListener(keyEvent, (e) => this.OnInnerEvent(e));
        }
    }
    removeInnerEventListenerAgent(editor) {
        for (let keyEvent in E_EVENT_KEY) {
            editor.removeEventListener(keyEvent, (e) => this.OnInnerEvent(e));
        }
    }
    OnInnerEvent(evt) {
        let e = evt;
        editorLogger.LogEvent(new Date(), " OnInnerEvent==>", e);
        let ret = this.dispatchEditorsInstanceEvent(e);
        if (ret == false) {
            evt.preventDefault();
        }
    }
}
