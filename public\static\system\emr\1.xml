<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.397'  />
					<Space count='7' />
					<Font size='0.767' cfg='1' />24  11小时内入出院记录</Paragraph>
				<Paragraph>
					<Font size='0.397' cfg='0' />
					<Space count='20' />
					<Font size='0.370' cfg='1' />Admission/Discharge<Space />Note<Space />for<Space />Patient<Space />Admitted<Space />Within<Space />24<Space />Hours</Paragraph>
				<Paragraph>
					<Separator height='0.123' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' cfg='0' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' />
					<Space count='2' />
					<Font cfg='0' />
					<Space count='8' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' />
					<Space />
					<Font cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='5' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住院科室名称'>
						<Hint cfg='1000'>
							<Font color='808080' />住院科室名称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='14' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' />
					<Space />
					<Font cfg='0' />
					<Space />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />H01-06-YW-015<Space count='141' />2014-03-A1</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='17' cols='4' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='80' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出<Space />生<Space />地：<Font cfg='0' />
									<Element name='EMRCategory' cfg='30000' hint='籍贯'>
										<Hint cfg='1000'>
											<Font color='808080' />籍贯</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='80' width='4.356' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />民<Space count='3' />族：<Font cfg='0' />
									<Element name='T2FolkDesc' cfg='30000' hint='民族名称'>
										<Hint cfg='1000'>
											<Font color='808080' />民族名称</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='80' width='4.356' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />职业：<Font cfg='0' />
									<Element name='CareerCode' cfg='30000' hint='职业'>
										<Hint cfg='1000'>
											<Font color='808080' />职业</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />婚<Space count='4' />姻：<Font cfg='0' />
									<Element cfg='30000' hint='婚姻'>
										<Hint cfg='1000'>
											<Font color='808080' />婚姻</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />住<Space count='3' />址：<Font cfg='0' />
									<Element name='EMRContactFlagAddr' cfg='30000' hint='同联系地址'>
										<Hint cfg='1000'>
											<Font color='808080' />同联系地址</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.687'>
							<Cell xCfg='1' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />联系电话：<Font cfg='0' />
									<Element name='EMRTelMobile' cfg='30000' hint='移动电话'>
										<Hint cfg='1000'>
											<Font color='808080' />移动电话</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />电子邮件（E-Mail)<Font cfg='0' />：<Element name='EMREmail' cfg='30000' hint='电子邮件'>
										<Hint cfg='1000'>
											<Font color='808080' />电子邮件</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院时间：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />问诊时间：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院时间：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />过敏药物、食物：<Font cfg='0' />
									<Element name='EMRDrugAllergicIN' cfg='30000' hint='药物过敏史'>
										<Hint cfg='1000'>
											<Font color='808080' />药物过敏史</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院前用药史：<Font cfg='0' />
									<Element name='HOBookTakeDrugRecent' cfg='30000' hint='近期用药史'>
										<Hint cfg='1000'>
											<Font color='808080' />近期用药史</Hint>
									</Element>
									<Font color='0' />
									<Element name='HOBookTakeDrugLongTerm' cfg='30000' hint='长期用药史'>
										<Hint cfg='1000'>
											<Font color='808080' />长期用药史</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.712' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院方式：<Font cfg='0' />
									<Element cfg='30000' hint='入院方式'>
										<Hint cfg='1000'>
											<Font color='808080' />入院方式</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />病史陈述者姓名：<Font cfg='0' />
									<Element cfg='30000' hint='病史陈述者姓名'>
										<Hint cfg='1000'>
											<Font color='808080' />病史陈述者姓名</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />主<Space count='4' />诉：<Font cfg='0' />
									<Element name='CCHPIIN' cfg='30000' hint='（促使患者就诊的最主要症状<或体征>及持续时间）'>
										<Hint cfg='1000'>
											<Font color='808080' />（促使患者就诊的最主要症状&lt;或体征&gt;及持续时间）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />现<Space />病<Space />史：<Font cfg='0' />
									<Element name='SOAPCurSymIN' cfg='30000' hint='（内容包括起病情况、主要症状的系统描述、病情的发展及演变、起病后诊疗经过、与本次疾病有关的病史及有意义的阴性病史、起病以来一般情况）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括起病情况、主要症状的系统描述、病情的发展及演变、起病后诊疗经过、与本次疾病有关的病史及有意义的阴性病史、起病以来一般情况）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />住院经过：<Font cfg='0' />
									<Element cfg='30000' hint='(内容包括住院期间诊疗过程效果，重要的药品治疗（药物名称、剂量、使用频次、途径）等，已施行的诊断性和治疗性的操作，凡接受手术治疗患者，应详细记录所做手术方式、术后主要治疗方式及效果、病理切片结果等)'>
										<Hint cfg='1000'>
											<Font color='808080' />(内容包括住院期间诊疗过程效果，重要的药品治疗（药物名称、剂量、使用频次、途径）等，已施行的诊断性和治疗性的操作，凡接受手术治疗患者，应详细记录所做手术方式、术后主要治疗方式及效果、病理切片结果等)</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院诊断：<Font cfg='0' />
									<Element cfg='30000' hint='（出院诊断）'>
										<Hint cfg='1000'>
											<Font color='808080' />（出院诊断）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出院医嘱：<Font cfg='0' />
									<Element cfg='30000' hint='内容包括直系亲属的健康情况，有无传染病史及遗传病史或患者类似疾病病史，如死亡应记录死因及日期'>
										<Hint cfg='1000'>
											<Font color='808080' />内容包括直系亲属的健康情况，有无传染病史及遗传病史或患者类似疾病病史，如死亡应记录死因及日期</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />随访指导：<Font cfg='0' />
									<Element name='EMRPtFamilyHistory' cfg='30000' hint='家族史'>
										<Hint cfg='1000'>
											<Font color='808080' />家族史</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />交通需求：<Font cfg='0' />
									<Element name='SOAPMediExamIN' cfg='30000' hint='应当根据专科需要记录专科特殊情况，包括与专科有关的全面体格检查内容。'>
										<Hint cfg='1000'>
											<Font color='808080' />应当根据专科需要记录专科特殊情况，包括与专科有关的全面体格检查内容。</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />医师签名/工号：<Space count='16' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />上级医生签名：<Space count='17' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='10' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分' beforeTag='时  间：'>
										<BeforeTag cfg='4'>
											<Font color='ff' />时间：</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
