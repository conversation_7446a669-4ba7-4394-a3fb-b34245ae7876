<template>
    <!-- 编辑器菜单 -->
    <DocumentMenus class="h-left" />
    <!-- @addEditorInstance="OnShowMoreEditorInstance"
        @removeEditorInstance="OnHideLastEditorInstance" @closeDocEditor="OnCloseDocEditor"
        @closeAllDocEditor="OnCloseAllDocEditor" @docOpt="OnDocOpt" -->
    <!-- 编辑器功能菜单盒子 -->
    <el-tabs type="border-card">
        <el-tab-pane label="编辑">
            <EditTags></EditTags>
        </el-tab-pane>
        <el-tab-pane label="插入">
            <EditorInstall @installBtnClick="getSelectOpera"></EditorInstall>
        </el-tab-pane>
        <el-tab-pane label="功能">
            <FuncTab @OnFuncClick="OnFuncClick"></FuncTab>
        </el-tab-pane>
    </el-tabs>
    <!-- 患者信息栏 -->
    <PatientInfo :patientinfo="patientinfo" />
</template>

<script setup>
import { ref } from 'vue'
import PatientInfo from './PatientInfo.vue'
import DocumentMenus from './DocumentMenus.vue'
// import EditTags from './EditTags.vue'
// import EditorInstall from './EditorInstall.vue'
// import FuncTab from './FuncTab.vue'
import {
    E_DOC_TYPE,
    E_DOC_FORMAT,
    E_DISPLAY_MODE,
    E_VIEW_MODE
} from '@/components/editor/ThinkEditor.Defined'

// 患者信息数据
const patientinfo = ref({
    patientId: '24313036',
    hospitalNumber: '0013689',
    height: '188',
    weight: '86',
    diagnosis: '高血压',
    mbm: '过敏',
    ward: '外科病区',
    nursingLevel: 'SAM'
})

// 获取当前焦点的标签页视图
const GetFocusTabsView = () => {
    // 实现获取当前焦点标签页视图的逻辑
    return window.currentTabsView || null
}

// 处理选择操作
const getSelectOpera = async (typeItem) => {
    let currentTabsView = GetFocusTabsView()
    if (!currentTabsView) {
        console.warn('未找到当前标签页视图')
        return
    }

    const thinkEditor = currentTabsView.GetSelectedEditor()
    if (!thinkEditor) {
        console.warn('当前未选中文档，操作无效!')
        return
    }

    // 根据操作类型执行相应的操作
    switch (typeItem.val) {
        case 'insertTable':
            thinkEditor.InsertTable({ rowsCount: 3, colsCount: 3 })
            break
        case 'insertImage':
            // 实现插入图片的逻辑
            break
        // 添加其他操作类型的处理...
        default:
            console.log('未处理的操作类型:', typeItem.val)
    }
}

// 处理功能点击
const OnFuncClick = async (typeItem) => {
    let currentTabsView = GetFocusTabsView()
    if (!currentTabsView) {
        console.warn('未找到当前标签页视图')
        return
    }

    switch (typeItem.val) {
        case 'newDoc':
            // 创建空文档编辑器
            let docEditor = await currentTabsView.NewDocEditor('', E_DOC_TYPE.Entity)
            // 后续操作：示例，Tab页签切换显示当前文档
            currentTabsView.SelectDocEditor(docEditor.docName)
            break
        case 'openDoc':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.OpenDoc()
            }
            break
    }

    let currentEditor = currentTabsView.GetSelectedEditor()
    if (!currentEditor) {
        console.warn('当前未选中文档，操作无效!')
        return
    }

    // 将当前编辑器绑定到全局变量
    window.thinkEditor = currentEditor
    if (window.thinkEditorDemo) {
        window.thinkEditorDemo.bind(window.thinkEditor)
    }

    // 根据操作类型执行相应的操作
    switch (typeItem.val) {
        case 'saveDoc':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.SaveToXml()
            }
            // var dataBuffer = thinkEditor.GetDoc(E_DOC_FORMAT.XML, E_DOC_TYPE.Entity)
            // 将dataBuffer保存到数据库
            break
        case 'saveAsTemplate':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.SaveToXml(E_DOC_TYPE.Template)
            }
            break
        case 'toHTML':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.SaveToHtml()
            }
            break
        case 'toPdf':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.SaveToPdf()
            }
            break
        case 'toImg':
            if (window.thinkEditorDemo) {
                window.thinkEditorDemo.SaveToImg()
            }
            break
        case 'setPage':
            // 触发页面设置窗口
            currentEditor.DisplayPageSetWindow(E_DISPLAY_MODE.Show)
            break
        case 'cleanEdit':
            currentEditor.SetViewMode(E_VIEW_MODE.Edit, {
                hideElementHint: false
            })
            break
        case 'reviseEdit':
            currentEditor.SetEditMode(true, true, true, true)
            currentEditor.SetViewMode(E_VIEW_MODE.Edit, {
                hideElementHint: false,
                displayEditAuxiliary: true,
                displaySelectionEffect: true,
                displayReviseAuxiliary: true,
                displayReviseDel: true,
                displayQCInfobox: true,
                displayCommentInfobox: true,
                displayQCMode: true,
                displayCommentMode: true
            })
            break
        case 'cleanBrowser':
            currentEditor.SetViewMode(E_VIEW_MODE.Browse, {
                hideElementHint: true
            })
            break
        case 'reviseBrowser':
            currentEditor.SetViewMode(E_VIEW_MODE.Browse, {
                hideElementHint: true,
                displayEditAuxiliary: true,
                displaySelectionEffect: true,
                displayReviseAuxiliary: true,
                displayReviseDel: true,
                displayQCInfobox: false,
                displayCommentInfobox: false,
                displayQCMode: true,
                displayCommentMode: true
            })
            break
        case 'cleanPrint':
            {
                let printCfg = {}
                printCfg.printMode = 1 // 0:视图打印 1：矢量打印
                printCfg.view = {
                    mode: E_VIEW_MODE.Print,
                    hideElementHint: true
                }
                await currentEditor.PrintDocAsync(printCfg)
            }
            break
        case 'revisePrint':
            {
                let printCfg = {}
                printCfg.printMode = 1 // 0:视图打印 1：矢量打印
                printCfg.view = {
                    mode: E_VIEW_MODE.Print,
                    hideElementHint: true,
                    displayEditAuxiliary: false,
                    displaySelectionEffect: false,
                    displayReviseAuxiliary: true,
                    displayReviseDel: true,
                    displayQCInfobox: false,
                    displayCommentInfobox: false,
                    displayQCMode: true,
                    displayCommentMode: true
                }
                await currentEditor.PrintDocAsync(printCfg)
            }
            break
        case 'SelectPagePrint':
            {
                let printCfg = {}
                printCfg.printMode = 1 // 0:视图打印 1：矢量打印
                printCfg.view = {
                    mode: E_VIEW_MODE.SelectPagePrint
                }
                await currentEditor.PrintDocAsync(printCfg)
            }
            break
        case 'SelectContentPrint':
            {
                let printCfg = {}
                printCfg.printMode = 1 // 0:视图打印 1：矢量打印
                printCfg.view = {
                    mode: E_VIEW_MODE.SelectContentPrint
                }
                await currentEditor.PrintDocAsync(printCfg)
            }
            break
        case 'ContinuePrint':
            {
                let printCfg = {}
                printCfg.printMode = 1 // 0:视图打印 1：矢量打印
                printCfg.view = {
                    mode: E_VIEW_MODE.ContinuePrint
                }
                await currentEditor.PrintDocAsync(printCfg)
            }
            break
        default:
            console.log('未处理的操作类型:', typeItem)
    }
}
</script>

<style scoped>
/* 可以添加组件特定的样式 */
</style>