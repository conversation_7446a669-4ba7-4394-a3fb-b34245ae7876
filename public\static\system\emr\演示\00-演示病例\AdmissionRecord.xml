<?xml version='1.0' encoding='utf-8'?>
<Doc type='template' version='v3_2025-04-15 22:57:10' class='呼吸科' pagesCount='2' exclude='咳嗽;编程;' docId='234' attrs='eyJEb2NJRCI6IjIzNCJ9'>
	<Sections>
		<Section cfg='9' pagesCount='2' width='21.000' height='29.700' borderWidth='0.020' topPadding='2.540' bottomPadding='2.000' leftPadding='3.140' rightPadding='3.140'>
			<Attach backColor='ffffff' />
			<Header topMargin='2.000' topBorderWidth='0.020' bottomBorderWidth='0.100' leftBorderWidth='0.020' rightBorderWidth='0.020'>
				<Paragraph spaceAfter='0.100'>
					<Table rows='2' cols='2' padding='0.010'>
						<Row rowIdx='0' height='0.900'>
							<Cell width='10.874' borderWidth='0.020'>
								<Paragraph xCfg='2'>
									<Space count='19' />
									<Element cfg='30101' xCfg='1' hint='机构:机构名' sourceClass='认证扩展' sourceId='机构名'>
										<Font size='0.776' />
										<Font size='0.423' />
									</Element>
								</Paragraph>
							</Cell>
							<Cell width='3.254' borderWidth='0.020' rowSpan='2'>
								<Paragraph xCfg='2'>
									<BarCode id='门诊号' width='1.508' height='1.508' cfg='33a'>MTIzODg5OTk=</BarCode>
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='1' height='1.005'>
							<Cell eCfg='41' width='10.874' borderWidth='0.020'>
								<Paragraph xCfg='2'>
									<Font size='0.564' />
									<Space count='12' />住院病历<Font size='0.423' />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph xCfg='0' spaceAfter='0.050'>住院号：<Element cfg='30000' hint='住院资料:住院号' width='3.000' sourceClass='住院资料' sourceId='住院号' />姓名：<Element cfg='30000' xCfg='1' hint='请输入' width='3.000' sourceClass='基本资料' sourceId='姓名' />科室：<Element cfg='30000' hint='请输入' width='3.000' sourceClass='住院资料' sourceId='科室' />
				</Paragraph>
			</Header>
			<Footer bottomMargin='0.300' borderWidth='0.020'>
				<Paragraph>
					<PageNum xCfg='4' width='2.963' height='0.503' lCfg='2'>
						<Unit eCfg='1' width='2.963' height='0.503'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
				</Paragraph>
			</Footer>
			<Body borderWidth='0.020'>
				<Paragraph />
				<Paragraph>
					<Table rows='7' cols='4' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
						<Row rowIdx='0' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>姓名</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element id='name' cfg='30000' xCfg='1' hint='请输入' sourceClass='基本资料' sourceId='姓名' />
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>职业</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='职业A' linkStr='、' hint='请选择'>
										<Feature lockOptId='2567' cfg='1' />
										<Feature lockOptId='' cfg='0' />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='1' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>性别</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='性别' linkStr='、' qcCfg='96' min='2' max='6' hint='请选择'>
										<Hint cfg='1000'>
											<Font color='808080' />请选择</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>工作单位</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' hint='请输入'>
										<Hint cfg='1000'>
											<Font color='808080' />请输入</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='2' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>年龄</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='1' qcClass='1' qcCfg='4' hint='请输入' />岁</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>住址</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element id='DE02.01.009.01' cfg='30000' inputMode='2' optionClass='系统' optionId='省份' linkStr='、' hint='请输入' sourceClass='系统' sourceId='省' />
									<Element id='DE02.01.009.02' cfg='30000' inputMode='2' optionClass='系统' optionId='市' linkStr='、' hint='请输入' sourceClass='系统' sourceId='市' />
									<Element id='DE02.01.009.03' cfg='30000' inputMode='2' optionClass='系统' optionId='区' linkStr='、' hint='请输入' sourceClass='系统' sourceId='区' />
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='3' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>出生地</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='省份' linkStr='、' hint='请选择'>
										<Hint cfg='1000'>
											<Font color='808080' />请选择</Hint>
										<Font color='0' />
									</Element>
									<Space />
									<Element cfg='30000' hint='请输入'>成都市</Element>
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>出生日期</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='3' time='1999-10-15 00:00:00' hint='请选择' />
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='4' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>婚姻</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='婚姻状况' linkStr='、' hint='请选择'>
										<Hint cfg='1000'>
											<Font color='808080' />请选择</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>入院时间</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='3' timeType='1' time='2019-09-09 09:16:23' hint='住院资料:入院时间' sourceClass='住院资料'>
										<Space />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='5' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>民族</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='民族' linkStr='、' hint='请选择'>
										<Hint cfg='1000'>
											<Font color='808080' />请选择</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>记录时间</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' inputMode='3' timeType='1' hint='请选择'>
										<Hint cfg='1000'>
											<Font color='808080' />请选择</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row rowIdx='6' height='0.660'>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>病史陈述者</Paragraph>
							</Cell>
							<Cell width='3.440' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' hint='请输入'>
										<Hint cfg='1000'>
											<Font color='808080' />请输入</Hint>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
							<Cell width='2.790' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>与患者关系</Paragraph>
							</Cell>
							<Cell width='5.140' borderWidth='0.020' borderStyle='1' borderColor='d9d9d9' topPadding='0.030' leftPadding='0.100' rightPadding='0.100'>
								<Paragraph>
									<Element cfg='30000' hint='请输入'>
										<Hint cfg='1000'>
											<Font color='808080' />请输入</Hint>
										<Font color='0' />
									</Element>
									<Font color='808080' />
									<Element cfg='30000' inputMode='2' optionClass='系统' optionId='可靠程度' linkStr='、' hint='请选择'>
										<Font color='0' />
									</Element>
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph>
					<Space count='3' />
					<SelectionBegin side='1' />
					<SelectionEnd side='1' />
				</Paragraph>
				<Paragraph id='主诉'>
					<Font cfg='1' />主诉：<Font cfg='0' />
					<Element id='简要诊断' cfg='30000' hint='简要诊断'>
						<Hint cfg='1000'>
							<Font color='808080' />简要诊断</Hint>
						<Font color='0' />
					</Element>/<Element cfg='30000' hint='既往治疗方式'>
						<Hint cfg='1000'>
							<Font color='808080' />既往治疗方式</Hint>
						<Font color='0' />
					</Element>
					<Space />
					<Element cfg='30000' xCfg='300' inputMode='2' optionClass='呼吸科' optionId='肺症状' linkStr='、' hint='请选择' />。</Paragraph>
				<Paragraph>
					<Font cfg='1' />现病史：<Font cfg='0' />
					<Element id='city' cfg='30000' hint='现病史'>现病史</Element>。现来我院就诊，于<Element cfg='30000' inputMode='2' optionClass='系统' optionId='入院方式' linkStr='、' hint='请选择'>
						<Hint cfg='1000'>
							<Font color='808080' />请选择</Hint>
						<Font color='0' />
					</Element>以<Element cfg='30000' inputMode='2' optionClass='呼吸科' optionId='院前诊断' linkStr='、' hint='院前诊断'>
						<Hint cfg='1000'>
							<Font color='808080' />院前诊断</Hint>
						<Font color='0' />
					</Element>收住院。自发病以来，患者精神<Element cfg='30000' inputMode='2' optionClass='系统' optionId='良好程度' linkStr='、' hint='请选择' sourceClass='系统' />，饮食<Element cfg='30000' inputMode='2' optionClass='系统' optionId='良好程度' linkStr='、' hint='请选择' />，<Element cfg='30000' inputMode='2' optionClass='体征' optionId='大便体征' linkStr='、' hint='请选择' />，<Element cfg='30000' inputMode='2' optionClass='体征' optionId='小便体征' linkStr='、' hint='请选择' />，<Element cfg='30000' inputMode='2' optionClass='体征' optionId='体重体征' linkStr='、' hint='请选择' />。</Paragraph>
				<Paragraph id='既往史'>
					<Font cfg='1' />既往史：<Font cfg='0' />一般健康状况：<Element cfg='30000' inputMode='2' optionClass='系统' optionId='良好程度' linkStr='、' hint='请选择' />。疾病史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='冠心病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='高血压病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='糖尿病病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='支气管哮喘病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='鼻窦炎病史' linkStr='、' hint='请选择' />。<Font cfg='1' />传染病史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='结核病史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />；<Font cfg='1' />预防接种史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='疫苗接种史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />手术外伤史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='手术史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />输血史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='输血史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />食物或药物过敏史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='过敏史' linkStr='、' hint='请选择'>
						<Expression>this.element.value=='有食物或药物过敏史' ? allergen_desc.element.visible=true:allergen_desc.element.visible=false</Expression>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />，<Element id='allergen_desc' cfg='30000' hint='描述过敏物'>
						<Hint cfg='1000'>
							<Font color='808080' />描述过敏物</Hint>
						<Font color='0' />
					</Element>。</Paragraph>
				<Paragraph id='个人史'>
					<Font cfg='1' />个人史：<Font cfg='0' />生于<Element cfg='30000' hint='请输入'>四川</Element>，久居<Element cfg='30000' hint='请输入'>成都天府新区</Element>。<Font cfg='1' />到过疫区接触疫水：<Element cfg='30000' inputMode='2' optionClass='系统' optionId='有无' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />吸烟史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='吸烟史' linkStr='、' hint='请选择'>
						<Expression>this.element.value=='吸烟' ? smoke.element.visible=true:smoke.element.visible=false</Expression>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />
					<Space count='2' />
					<Element id='smoke' cfg='30000' inputMode='2' linkStr='、' hint='请选择'>
						<Element cfg='30000' hint='请输入'>
							<Space />吸烟<Space />
							<Element cfg='30000' hint='输入烟龄'>
								<Hint cfg='1000'>
									<Font color='808080' />输入烟龄</Hint>
								<Font color='0' />
							</Element>
							<Space count='2' />年，每日约<Space />
							<Element cfg='30000' hint='输入支数'>
								<Hint cfg='1000'>
									<Font color='808080' />输入支数</Hint>
								<Font color='0' />
							</Element>
							<Space count='3' />支，<Element cfg='30000' inputMode='2' optionClass='病史' optionId='戒烟' linkStr='、' hint='是否戒烟'>
								<Hint cfg='1000'>
									<Font color='808080' />是否戒烟</Hint>
								<Font color='0' />
							</Element>
							<Space count='2' />
						</Element>
						<Space />
					</Element>。<Font cfg='1' />粉尘接触史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='粉尘接触史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />禽鸟接触史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='禽鸟接触史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />毒物或放射性接触史：<Element cfg='30000' inputMode='2' optionClass='病史' optionId='毒物或放射线接触史' linkStr='、' hint='请选择'>
						<Font cfg='0' />
						<Font cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />特殊嗜好：<Element cfg='30000' inputMode='2' optionClass='系统' optionId='有无' linkStr='、' hint='请选择'>
						<Hint cfg='1000'>
							<Font color='808080' cfg='0' />请选择</Hint>
						<Font color='0' cfg='1' />
					</Element>
					<Font cfg='0' />。<Font cfg='1' />治游史：<Font cfg='0' />
					<Element cfg='30000' inputMode='2' optionClass='系统' optionId='有无' linkStr='、' hint='请选择'>
						<Hint cfg='1000'>
							<Font color='808080' />请选择</Hint>
						<Font color='0' />
					</Element>。<Element cfg='30000' hint='其它个人史描述'>
						<Hint cfg='1000'>
							<Font color='808080' />其它个人史描述</Hint>
						<Font color='0' />
					</Element>。</Paragraph>
				<Paragraph>
					<Font cfg='1' />婚育史：<Font cfg='0' />
					<Element cfg='30000' inputMode='2' optionClass='系统' optionId='婚姻状况' linkStr='、' hint='请选择'>
						<Expression>this.element.value=='未婚' ?married.element.visible=false:married.element.visible=true</Expression>
					</Element>。<Element id='married' cfg='30000' hint='请输入'>
						<Space />结婚年龄：<Element cfg='30000' hint='年龄'>
							<Hint cfg='1000'>
								<Font color='808080' />年龄</Hint>
							<Font color='0' />
						</Element>
						<Space />岁，配偶健康<Element cfg='30000' inputMode='2' optionClass='系统' optionId='良好程度' linkStr='、' hint='请选择'>
							<Hint cfg='1000'>
								<Font color='808080' />请选择</Hint>
							<Font color='0' />
						</Element>
						<Space />，<Element cfg='30000' inputMode='2' optionClass='系统' optionId='生育情况' linkStr='、' hint='请选择' />。<Space />
					</Element>
					<Space />
				</Paragraph>
				<Paragraph id='家族史'>
					<Font cfg='1' />家族史：<Font cfg='0' />
					<Element cfg='30000' inputMode='2' optionClass='病史' optionId='家族遗传病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='家族肺部疾病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='家族肿瘤疾病史' linkStr='、' hint='请选择' />；<Element cfg='30000' inputMode='2' optionClass='病史' optionId='传染病史' linkStr='、' hint='请选择' />；<Element cfg='30000' hint='描述其他家族史'>
						<Hint cfg='1000'>
							<Font color='808080' />描述其他家族史</Hint>
						<Font color='0' />
					</Element>。</Paragraph>
				<Paragraph>
					<Space />
				</Paragraph>
				<Paragraph>
					<Font color='ff00' />元素选项<Space />和选框基于知识库，能够快速输入和选择。<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font size='0.564' color='ff0000' />元素联动示例，请选择“已婚”/“未婚”：<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font size='0.423' cfg='1' />婚育史：<Font cfg='0' />
					<Element cfg='30000' inputMode='2' optionClass='系统' optionId='婚姻状况' linkStr='、' hint='请选择'>
						<Expression>this.element.value=='未婚' ?married_1.element.visible=false:married_1.element.visible=true</Expression>
					</Element>。<Element id='married_1' cfg='30000' hint='请输入'>
						<Space />结婚年龄：<Element cfg='30000' inputMode='1' qcClass='1' qcCfg='8' count='1' hint='年龄'>
							<Hint cfg='1000'>
								<Font color='808080' />年龄</Hint>
							<Font color='0' />
						</Element>
						<Space />岁，配偶健康<Element cfg='30000' inputMode='2' optionClass='系统' optionId='良好程度' linkStr='、' hint='请选择'>
							<Hint cfg='1000'>
								<Font color='808080' />请选择</Hint>
							<Font color='0' />
						</Element>
						<Space />，<Element cfg='30000' inputMode='2' optionClass='系统' optionId='生育情况' linkStr='、' hint='请选择' />。<Space />
					</Element>
					<Space />
				</Paragraph>
				<Paragraph>
					<Font size='0.564' color='ff0000' />选框联动示例：<Font color='0' />
				</Paragraph>
				<Paragraph>
					<CheckBox xCfg='11'>
						<Expression event='1'>this.checkbox.checked==true ?any_where.element.visible=true :any_where.ele.visible=false</Expression>
						<Font size='0.423' />
						<CF />勾选是否显示任意位置元素</CheckBox>
				</Paragraph>
				<Paragraph>
					<Element id='any_where' cfg='30000' hint='任意位置元素'>
						<Hint cfg='1000'>
							<Font color='808080' />任意位置元素</Hint>
						<Font color='0' />
					</Element>
				</Paragraph>
				<Paragraph>
					<Font size='0.564' color='ff0000' />可编程的事件表达式语句，示例如下：<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font size='0.423' />this.element.value=='未婚'<Space count='2' />?<Space />married_1.element.visible=false<Space />:<Space />married_1.element.visible=true</Paragraph>
				<Paragraph>
					<Font size='0.564' color='ff0000' />信译：可自由输入的医学表达式。<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font color='ff0000' />其他：公式是图片，无法直接编。<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font color='ff0000' />丰富的公式支持<Formula xCfg='1' width='6.350' height='1.640' lCfg='2'>
						<Unit eCfg='45' width='2.540' height='0.820' padding='0.190'>
							<Paragraph>
								<Element cfg='30000' inputMode='3' time='1988-10-23 00:00:00' hint='初潮年龄' />
							</Paragraph>
						</Unit>
						<Unit eCfg='45' width='0.635' height='0.820' padding='0.190'>
							<Paragraph xCfg='2'>
								<Element cfg='30000' inputMode='1' qcClass='1' qcCfg='100' min='1' max='31' hint='经期' />
							</Paragraph>
						</Unit>
						<Unit eCfg='45' width='1.270' height='0.820' padding='0.190'>
							<Paragraph xCfg='2'>
								<Element cfg='30000' hint='周期'>
									<Hint cfg='1000'>
										<Font color='808080' />周期</Hint>
									<Font color='0' />
								</Element>
							</Paragraph>
						</Unit>
						<Unit eCfg='45' width='2.540' height='0.820' padding='0.190'>
							<Paragraph>
								<Element cfg='30000' inputMode='3' time='2019-10-16 00:00:00' hint='末次月经' />
							</Paragraph>
						</Unit>
					</Formula>
				</Paragraph>
				<Paragraph />
				<Paragraph>
					<Font size='0.564' color='ff0000' />全结构化<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font color='ff0000' />信译：业内唯一的“全结构化”的病历格式<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font color='ff0000' />其它：非结构化或半结构化<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font size='0.423' />直接满足互联互通测评要求，唯一能做到的直接提取“元素”、“段落”内容的电子病历</Paragraph>
				<Paragraph />
			</Body>
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes>
			<Options class='系统'>
				<Option id='职业A'>
					<Item id='儿啰童' group='1'>儿啰童</Item>
					<Item id='学生' group='1'>学生</Item>
					<Item id='技术人员' group='1'>技术人员</Item>
					<Item id='军人' group='1'>军人</Item>
					<Item id='工人' group='1'>工人</Item>
					<Item id='农民' group='1'>农民</Item>
					<Item id='服务业' group='1'>服务业</Item>
					<Item id='无业' group='1'>无业</Item>
					<Item id='事业单位' group='1'>事业单位</Item>
					<Item id='金融业' group='1'>金融业</Item>
					<Item id='运输业' group='1'>运输业</Item>
					<Item id='其它' group='1'>其它</Item>
				</Option>
				<Option id='性别'>
					<Item id='男性' group='1'>男性</Item>
					<Item id='女性' group='1'>女性</Item>
					<Item id='未说明的性别' group='1'>未说明的性别</Item>
					<Item id='未知的性别' group='1'>未知的性别</Item>
				</Option>
				<Option id='省份'>
					<Item id='北京市' group='1' weight='1.000'>北京市</Item>
					<Item id='天津市' group='1' weight='1.000'>天津市</Item>
					<Item id='河北省' group='1' weight='1.000'>河北省</Item>
					<Item id='山西省' group='1' weight='1.000'>山西省</Item>
					<Item id='内蒙古自治区' group='1' weight='1.000'>内蒙古自治区</Item>
					<Item id='辽宁省' group='1' weight='1.000'>辽宁省</Item>
					<Item id='吉林省' group='1' weight='1.000'>吉林省</Item>
					<Item id='黑龙江省' group='1' weight='1.000'>黑龙江省</Item>
					<Item id='上海市' group='1' weight='1.000'>上海市</Item>
					<Item id='江苏省' group='1' weight='1.000'>江苏省</Item>
					<Item id='浙江省' group='1' weight='1.000'>浙江省</Item>
					<Item id='安徽省' group='1' weight='1.000'>安徽省</Item>
					<Item id='福建省' group='1' weight='1.000'>福建省</Item>
					<Item id='江西省' group='1' weight='1.000'>江西省</Item>
					<Item id='山东省' group='1' weight='1.000'>山东省</Item>
					<Item id='河南省' group='1' weight='1.000'>河南省</Item>
					<Item id='湖北省' group='1' weight='1.000'>湖北省</Item>
					<Item id='湖南省' group='1' weight='1.000'>湖南省</Item>
					<Item id='广东省' group='1' weight='1.000'>广东省</Item>
					<Item id='广西壮族自治区' group='1' weight='1.000'>广西壮族自治区</Item>
					<Item id='海南省' group='1' weight='1.000'>海南省</Item>
					<Item id='重庆市' group='1' weight='1.000'>重庆市</Item>
					<Item id='四川省' group='1' weight='1.000'>四川省</Item>
					<Item id='贵州省' group='1' weight='1.000'>贵州省</Item>
					<Item id='云南省' group='1' weight='1.000'>云南省</Item>
					<Item id='西藏自治区' group='1' weight='1.000'>西藏自治区</Item>
					<Item id='陕西省' group='1' weight='1.000'>陕西省</Item>
					<Item id='甘肃省' group='1' weight='1.000'>甘肃省</Item>
					<Item id='青海省' group='1' weight='1.000'>青海省</Item>
					<Item id='宁夏回族自治区' group='1' weight='1.000'>宁夏回族自治区</Item>
					<Item id='新疆维吾尔自治区' group='1' weight='1.000'>新疆维吾尔自治区</Item>
				</Option>
				<Option id='市'>
					<Item id='成都市' group='1' weight='1.000'>成都市</Item>
					<Item id='自贡市' group='1' weight='1.000'>自贡市</Item>
					<Item id='攀枝花市' group='1' weight='1.000'>攀枝花市</Item>
					<Item id='泸州市' group='1' weight='1.000'>泸州市</Item>
					<Item id='德阳市' group='1' weight='1.000'>德阳市</Item>
					<Item id='绵阳市' group='1' weight='1.000'>绵阳市</Item>
					<Item id='广元市' group='1' weight='1.000'>广元市</Item>
					<Item id='遂宁市' group='1' weight='1.000'>遂宁市</Item>
					<Item id='内江市' group='1' weight='1.000'>内江市</Item>
					<Item id='乐山市' group='1' weight='1.000'>乐山市</Item>
					<Item id='南充市' group='1' weight='1.000'>南充市</Item>
					<Item id='眉山市' group='1' weight='1.000'>眉山市</Item>
					<Item id='宜宾市' group='1' weight='1.000'>宜宾市</Item>
					<Item id='广安市' group='1' weight='1.000'>广安市</Item>
					<Item id='达州市' group='1' weight='1.000'>达州市</Item>
					<Item id='雅安市' group='1' weight='1.000'>雅安市</Item>
					<Item id='巴中市' group='1' weight='1.000'>巴中市</Item>
					<Item id='资阳市' group='1' weight='1.000'>资阳市</Item>
					<Item id='阿坝藏族羌族自治州' group='1' weight='1.000'>阿坝藏族羌族自治州</Item>
					<Item id='甘孜藏族自治州' group='1' weight='1.000'>甘孜藏族自治州</Item>
					<Item id='凉山彝族自治州' group='1' weight='1.000'>凉山彝族自治州</Item>
				</Option>
				<Option id='区' />
				<Option id='婚姻状况'>
					<Item id='未婚' group='1'>未婚</Item>
					<Item id='已婚' group='1'>已婚</Item>
					<Item id='丧偶' group='1'>丧偶</Item>
					<Item id='离婚' group='1'>离婚</Item>
					<Item id='其它' group='1'>其它</Item>
				</Option>
				<Option id='民族'>
					<Item id='汉族' group='1'>汉族</Item>
					<Item id='蒙古族' group='1'>蒙古族</Item>
					<Item id='回族' group='1'>回族</Item>
					<Item id='藏族' group='1'>藏族</Item>
					<Item id='维吾尔族' group='1'>维吾尔族</Item>
					<Item id='苗族' group='1'>苗族</Item>
					<Item id='彝族' group='1'>彝族</Item>
					<Item id='壮族' group='1'>壮族</Item>
					<Item id='布依族' group='1'>布依族</Item>
					<Item id='朝鲜族' group='1'>朝鲜族</Item>
					<Item id='满族' group='1'>满族</Item>
					<Item id='侗族' group='1'>侗族</Item>
					<Item id='瑶族' group='1'>瑶族</Item>
					<Item id='白族' group='1'>白族</Item>
					<Item id='土家族' group='1'>土家族</Item>
					<Item id='哈尼族' group='1'>哈尼族</Item>
					<Item id='哈萨克族' group='1'>哈萨克族</Item>
					<Item id='傣族' group='1'>傣族</Item>
					<Item id='黎族' group='1'>黎族</Item>
					<Item id='傈僳族' group='1'>傈僳族</Item>
					<Item id='佤族' group='1'>佤族</Item>
					<Item id='畲族' group='1'>畲族</Item>
					<Item id='高山族' group='1'>高山族</Item>
					<Item id='拉祜族' group='1'>拉祜族</Item>
					<Item id='水族' group='1'>水族</Item>
					<Item id='东乡族' group='1'>东乡族</Item>
					<Item id='纳西族' group='1'>纳西族</Item>
					<Item id='景颇族' group='1'>景颇族</Item>
					<Item id='柯尔克孜族' group='1'>柯尔克孜族</Item>
					<Item id='土族' group='1'>土族</Item>
					<Item id='达斡尔族' group='1'>达斡尔族</Item>
					<Item id='仫佬族' group='1'>仫佬族</Item>
					<Item id='羌族' group='1'>羌族</Item>
					<Item id='布朗族' group='1'>布朗族</Item>
					<Item id='撒拉族' group='1'>撒拉族</Item>
					<Item id='毛南族' group='1'>毛南族</Item>
					<Item id='仡佬族' group='1'>仡佬族</Item>
					<Item id='锡伯族' group='1'>锡伯族</Item>
					<Item id='阿昌族' group='1'>阿昌族</Item>
					<Item id='普米族' group='1'>普米族</Item>
					<Item id='塔吉克族' group='1'>塔吉克族</Item>
					<Item id='怒族' group='1'>怒族</Item>
					<Item id='乌孜别克族' group='1'>乌孜别克族</Item>
					<Item id='俄罗斯族' group='1'>俄罗斯族</Item>
					<Item id='鄂温克族' group='1'>鄂温克族</Item>
					<Item id='德昂族' group='1'>德昂族</Item>
					<Item id='保安族' group='1'>保安族</Item>
					<Item id='裕固族' group='1'>裕固族</Item>
					<Item id='京族' group='1'>京族</Item>
					<Item id='塔塔尔族' group='1'>塔塔尔族</Item>
					<Item id='独龙族' group='1'>独龙族</Item>
					<Item id='鄂伦春族' group='1'>鄂伦春族</Item>
					<Item id='赫哲族' group='1'>赫哲族</Item>
					<Item id='门巴族' group='1'>门巴族</Item>
					<Item id='珞巴族' group='1'>珞巴族</Item>
					<Item id='基诺族' group='1'>基诺族</Item>
				</Option>
				<Option id='可靠程度'>
					<Item id='可靠' group='1'>可靠</Item>
					<Item id='不可靠' group='1'>不可靠</Item>
				</Option>
				<Option id='入院方式'>
					<Item id='门诊' group='1'>门诊</Item>
					<Item id='急诊' group='1'>急诊</Item>
				</Option>
				<Option id='良好程度'>
					<Item id='良好' group='1' weight='1.000'>良好</Item>
					<Item id='一般' group='1' weight='1.000'>一般</Item>
					<Item id='较差' group='1' weight='1.000'>较差</Item>
					<Item id='差' group='1' weight='1.000'>差</Item>
				</Option>
				<Option id='有无'>
					<Item id='有' group='1'>有</Item>
					<Item id='无' group='1'>无</Item>
				</Option>
				<Option id='生育情况'>
					<Item id='无子女' group='1'>无子女</Item>
					<Item id='育有' group='1'>育有</Item>
				</Option>
			</Options>
			<Options class='呼吸科'>
				<Option id='肺症状'>
					<Item id='咳嗽' group='1' weight='1.000'>咳嗽</Item>
					<Item id='咳痰' group='1' weight='1.000'>咳痰</Item>
					<Item id='气促' group='1' weight='1.000'>气促</Item>
					<Item id='呼吸困难' group='1' weight='1.000'>呼吸困难</Item>
					<Item id='喘息' group='1' weight='1.000'>喘息</Item>
					<Item id='胸闷' group='1' weight='1.000'>胸闷</Item>
					<Item id='心悸' group='1' weight='1.000'>心悸</Item>
					<Item id='水肿' group='1' weight='1.000'>水肿</Item>
					<Item id='发热' group='1' weight='1.000'>发热</Item>
					<Item id='咯血' group='1' weight='1.000'>咯血</Item>
				</Option>
				<Option id='院前诊断'>
					<Item id='慢性阻塞性肺疾病' group='1'>慢性阻塞性肺疾病</Item>
					<Item id='慢性阻塞性肺疾病急性加重' group='1'>慢性阻塞性肺疾病急性加重</Item>
					<Item id='慢性肺源性心脏病' group='1'>慢性肺源性心脏病</Item>
					<Item id='慢性支气管炎' group='1'>慢性支气管炎</Item>
					<Item id='肺气肿' group='1'>肺气肿</Item>
					<Item id='呼吸衰竭' group='1'>呼吸衰竭</Item>
				</Option>
			</Options>
			<Options class='体征'>
				<Option id='大便体征'>
					<Item id='大便正常' group='1' weight='1.000'>大便正常</Item>
					<Item id='有便秘' group='1' weight='1.000'>有便秘</Item>
					<Item id='有腹泻' group='1' weight='1.000'>有腹泻</Item>
				</Option>
				<Option id='小便体征'>
					<Item id='小便正常' group='1'>小便正常</Item>
					<Item id='尿少' group='1'>尿少</Item>
					<Item id='无尿' group='1'>无尿</Item>
					<Item id='夜尿频多' group='1'>夜尿频多</Item>
				</Option>
				<Option id='体重体征'>
					<Item id='体重无明显变化' group='1'>体重无明显变化</Item>
					<Item id='体重减轻' group='1'>体重减轻</Item>
					<Item id='体重增加' group='1'>体重增加</Item>
				</Option>
			</Options>
			<Options class='病史'>
				<Option id='冠心病史'>
					<Item id='否认冠心病史' group='1'>否认冠心病史</Item>
					<Item id='有冠心病史' group='1'>有冠心病史</Item>
				</Option>
				<Option id='高血压病史'>
					<Item id='否认高血压病史' group='1'>否认高血压病史</Item>
					<Item id='有高血压病史' group='1'>有高血压病史</Item>
				</Option>
				<Option id='糖尿病病史'>
					<Item id='否认糖尿病病史' group='1'>否认糖尿病病史</Item>
					<Item id='有糖尿病病史' group='1'>有糖尿病病史</Item>
				</Option>
				<Option id='支气管哮喘病史'>
					<Item id='否认支气管哮喘病史' group='1'>否认支气管哮喘病史</Item>
					<Item id='有支气管哮喘病史' group='1'>有支气管哮喘病史</Item>
				</Option>
				<Option id='鼻窦炎病史'>
					<Item id='否认鼻窦炎病史' group='1'>否认鼻窦炎病史</Item>
					<Item id='有鼻窦炎病史' group='1'>有鼻窦炎病史</Item>
				</Option>
				<Option id='结核病史'>
					<Item id='否认结核病史' group='1'>否认结核病史</Item>
					<Item id='有肺结核病史' group='1'>有肺结核病史</Item>
					<Item id='有其它部位结核病史' group='1'>有其它部位结核病史</Item>
				</Option>
				<Option id='疫苗接种史'>
					<Item id='随社会人群进行' group='1'>随社会人群进行</Item>
					<Item id='不详' group='1'>不详</Item>
				</Option>
				<Option id='手术史'>
					<Item id='否认手术外伤史' group='1'>否认手术外伤史</Item>
					<Item id='胸部手术史' group='1'>胸部手术史</Item>
				</Option>
				<Option id='输血史'>
					<Item id='否认输血史' group='1'>否认输血史</Item>
					<Item id='有输血史' group='1'>有输血史</Item>
					<Item id='曾出现输血反应' group='1'>曾出现输血反应</Item>
				</Option>
				<Option id='过敏史'>
					<Item id='否认食物或药物过敏史' group='1'>否认食物或药物过敏史</Item>
					<Item id='有食物或药物过敏史' group='1'>有食物或药物过敏史</Item>
				</Option>
				<Option id='吸烟史'>
					<Item id='否认吸烟' group='1' weight='1.000'>否认吸烟</Item>
					<Item id='吸烟' group='1' weight='1.000'>吸烟</Item>
					<Item id='有长期二手烟吸入史' group='1' weight='1.000'>有长期二手烟吸入史</Item>
				</Option>
				<Option id='戒烟'>
					<Item id='已戒烟' group='1'>已戒烟</Item>
					<Item id='未戒烟' group='1'>未戒烟</Item>
				</Option>
				<Option id='粉尘接触史'>
					<Item id='否认粉尘接触史' group='1'>否认粉尘接触史</Item>
					<Item id='有粉尘接触史' group='1'>有粉尘接触史</Item>
				</Option>
				<Option id='禽鸟接触史'>
					<Item id='否认禽鸟接触史' group='1'>否认禽鸟接触史</Item>
					<Item id='有禽鸟接触史' group='1'>有禽鸟接触史</Item>
				</Option>
				<Option id='毒物或放射线接触史'>
					<Item id='否认毒物或放射线接触史' group='1'>否认毒物或放射线接触史</Item>
					<Item id='有毒物或放射线接触史' group='1'>有毒物或放射线接触史</Item>
				</Option>
				<Option id='家族遗传病史'>
					<Item id='否认家族遗传病史' group='1'>否认家族遗传病史</Item>
					<Item id='有家族遗传病史' group='1'>有家族遗传病史</Item>
				</Option>
				<Option id='家族肺部疾病史'>
					<Item id='否认家族肺部疾病史' group='1'>否认家族肺部疾病史</Item>
					<Item id='有家族肺部疾病史' group='1'>有家族肺部疾病史</Item>
				</Option>
				<Option id='家族肿瘤疾病史'>
					<Item id='否认家族肿瘤疾病史' group='1'>否认家族肿瘤疾病史</Item>
					<Item id='有家族肿瘤疾病史' group='1'>有家族肿瘤疾病史</Item>
				</Option>
				<Option id='传染病史'>
					<Item id='否认传染病史' group='1'>否认传染病史</Item>
					<Item id='有传染病史' group='1'>有传染病史</Item>
				</Option>
			</Options>
			<Options class=''>
				<Option />
			</Options>
		</OptionRes>
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>

