<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='2'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='12' lineSpaceValue='1.500'>
					<Font size='0.397'  />
					<Space count='8' />
					<Font size='0.767' cfg='1' />新生儿科出院记录</Paragraph>
				<Paragraph>
					<Font size='0.397' cfg='0' />
					<Space count='10' />
					<Font size='0.423' cfg='1' />
					<Space count='2' />
					<Font size='0.370' />Discharge<Space />Note<Space count='2' />Neonatology</Paragraph>
				<Paragraph xCfg='10'>
					<Separator height='0.123' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' cfg='0' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' />
					<Space />
					<Font cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='5' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住  院  科  室  名  称'>
						<Hint cfg='1000'>
							<Font color='808080' />住<Space count='2' />院<Space count='2' />科<Space count='2' />室<Space count='2' />名<Space count='2' />称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' />
					<Space />
					<Font cfg='0' />
					<Space count='2' />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />H01-06-YW-062<Space count='41' />
					<PageNum xCfg='4' width='4.143' height='0.469' lCfg='2'>
						<Unit width='4.143' height='0.469'>
							<Paragraph xCfg='2'>
								<Font size='0.370'  />第1页/共2页</Paragraph>
						</Unit>
					</PageNum>
					<Space count='49' />2022-12-A2</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='4' cols='4' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Table id='table5' rows='6' cols='3' padding='0.012'>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='13.305' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' rowSpan='6'>
												<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.388'  />出生日期：<Element name='POutDate' cfg='30000' hint='出院日期'>
														<Hint cfg='1000'>
															<Font color='808080' />出院日期</Hint>
													</Element>
													<Font color='0' />
													<Space count='2' />出生体重<Font cfg='404' />
													<Element cfg='30000' hint='数值'>
														<Hint cfg='1000'>
															<Font color='808080' />数值</Hint>
													</Element>
													<Font color='0' cfg='0' />kg<Space count='2' />出院体重<Font cfg='404' />
													<Element cfg='30000' hint='数值'>
														<Hint cfg='1000'>
															<Font color='808080' />数值</Hint>
													</Element>
													<Font color='0' cfg='0' />kg</Paragraph>
												<Paragraph leftIndent='0.000' rightIndent='0.000' spaceBefore='0.000' spaceAfter='0.000'>家长姓名<Font size='0.397' cfg='404' />
													<Element cfg='30000' hint='姓名'>
														<Hint cfg='1000'>
															<Font color='808080' />姓名</Hint>
													</Element>
													<Font color='0' cfg='0' />
													<Space />住址<Font cfg='404' />
													<Element cfg='30000' hint='详细地址'>
														<Hint cfg='1000'>
															<Font color='808080' />详细地址</Hint>
													</Element>
													<Font color='0' cfg='0' />
													<Space />电话<Font cfg='404' />
													<Element cfg='30000' hint='号码'>
														<Hint cfg='1000'>
															<Font color='808080' />号码</Hint>
													</Element>
													<Font color='0' />
												</Paragraph>
												<Paragraph>
													<Font cfg='0' />出生地点<Font cfg='404' />
													<Element cfg='30000' hint='具体医疗结构名称或其他地点'>
														<Hint cfg='1000'>
															<Font color='808080' />具体医疗结构名称或其他地点</Hint>
													</Element>
													<Font color='0' />
												</Paragraph>
												<Paragraph>
													<Font cfg='0' />住院日期<Element name='PINDate' cfg='30000' hint='入院日期'>
														<Hint cfg='1000'>
															<Font color='808080' />入院日期</Hint>
													</Element>
													<Font color='0' />至<Element name='POutDate' cfg='30000' hint='出院日期'>
														<Hint cfg='1000'>
															<Font color='808080' />出院日期</Hint>
													</Element>
													<Font color='0' />
													<Space count='2' />共<Font cfg='404' />
													<Element cfg='30000' hint='数值'>
														<Hint cfg='1000'>
															<Font color='808080' />数值</Hint>
													</Element>
													<Font color='0' cfg='0' />天</Paragraph>
												<Paragraph>
													<Font cfg='1' />入院诊断：</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />检查名称</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />检查号</Paragraph>
											</Cell>
										</Row>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />超声号</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />
												</Paragraph>
											</Cell>
										</Row>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />X线号</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />
												</Paragraph>
											</Cell>
										</Row>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />CT号</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />
												</Paragraph>
											</Cell>
										</Row>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />ECG号</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />
												</Paragraph>
											</Cell>
										</Row>
										<Row height='0.000' xCfg='1'>
											<Cell xCfg='1' eCfg='1' width='2.055' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />MRI号</Paragraph>
											</Cell>
											<Cell xCfg='1' eCfg='1' width='2.005' borderWidth='0.012' borderStyle='1' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
												<Paragraph xCfg='12' leftIndent='0.020' rightIndent='0.020' spaceBefore='0.020' spaceAfter='0.020' lineSpaceValue='1.500'>
													<Font size='0.397'  />
												</Paragraph>
											</Cell>
										</Row>
									</Table>
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />主<Space count='4' />诉：<Font cfg='0' />
									<Element name='CCHPIIN' cfg='30000' hint='病人主诉'>
										<Hint cfg='1000'>
											<Font color='808080' />病人主诉</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />现<Space />病<Space />史：<Font cfg='0' />
									<Element name='SOAPCurSymIN' cfg='30000' hint='现病史'>
										<Hint cfg='1000'>
											<Font color='808080' />现病史</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Element cfg='30000' hint='早产原因' beforeTag=','>
										<BeforeTag cfg='4'>
											<Font color='ff' />,</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />早产原因</Hint>
									</Element>
									<Font color='0' />Apgar评分：1分钟<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />分，5分钟<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />分，母亲孕产史<Element cfg='30000' hint='是否保胎' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Font color='0' />孕<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />月开始，药物<Font cfg='404' />
										<Element cfg='30000' hint='名称'>
											<Hint cfg='1000'>
												<Font color='808080' />名称</Hint>
										</Element>
									</Element>
									<Font color='0' cfg='0' />
									<Element cfg='30000' hint='用药信息' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Font color='0' />孕<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />周开始用药，药物<Font cfg='404' />
										<Element cfg='30000' hint='名称'>
											<Hint cfg='1000'>
												<Font color='808080' />名称</Hint>
										</Element>
										<Font color='0' cfg='0' />，<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />mg，q<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />H×<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />次×<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />疗程</Element>
									<Element cfg='30000' hint='疾病名称' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />疾病名称</Hint>
									</Element>
									<Font color='0' />
									<Element cfg='30000' hint='结果如何' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Font color='0' />结果<Font cfg='404' />
										<Element cfg='30000' hint='如何'>
											<Hint cfg='1000'>
												<Font color='808080' />如何</Hint>
										</Element>
									</Element>
									<Font color='0' cfg='0' />
									<Element cfg='30000' hint='结果如何' beforeTag='，' borderStyle='1' borderWidth='0.012'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Font color='0' />结果<Font cfg='404' />
										<Element cfg='30000' hint='如何'>
											<Hint cfg='1000'>
												<Font color='808080' />如何</Hint>
										</Element>
									</Element>
									<Font color='0' cfg='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，' borderStyle='1' borderWidth='0.012'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，' borderStyle='1' borderWidth='0.012'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font color='0' cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />羊水<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />脐带<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />胎盘<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />胎膜早破<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
					<Font size='0.397' cfg='0' />
				</Paragraph>
				<Paragraph leftIndent='0.000' rightIndent='0.000' spaceAfter='0.080'>
					<Table id='yuejingshiButtom' rows='11' cols='1' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />查体：<Space count='2' />T:<Font cfg='404' />
									<Element cfg='30000' inputMode='1' hint='体温'>
										<Hint cfg='1000'>
											<Font color='808080' />体温</Hint>
									</Element>
									<Font color='0' cfg='0' />℃<Space count='3' />
									<Font cfg='1' />P：<Font cfg='404' />
									<Element cfg='30000' inputMode='1' hint='脉搏'>
										<Hint cfg='1000'>
											<Font color='808080' />脉搏</Hint>
									</Element>
									<Font color='0' cfg='0' />次/分<Space count='2' />
									<Font cfg='1' />R:<Font cfg='0' />
									<Space />
									<Font cfg='404' />
									<Element cfg='30000' inputMode='1' hint='呼吸'>
										<Hint cfg='1000'>
											<Font color='808080' />呼吸</Hint>
									</Element>
									<Font color='0' cfg='0' />次/分<Space count='2' />
									<Font cfg='1' />SO<Font cfg='0' />2<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />%<Space count='3' />
									<Font cfg='1' />Wt<Font cfg='405' />
									<Element name='PEDXWeightIN' cfg='30000' hint='体重'>
										<Hint cfg='1000'>
											<Font color='808080' />体重</Hint>
									</Element>
									<Font color='0' cfg='0' />kg<Space count='3' />
								</Paragraph>
								<Paragraph leftIndent='0.000' rightIndent='0.000'>
									<Space count='7' />
									<Font cfg='1' />
									<Space />BP：<Font cfg='404' />
									<Element cfg='30000' inputMode='1' hint='收缩压'>
										<Hint cfg='1000'>
											<Font color='808080' />收缩压</Hint>
									</Element>
									<Font color='0' />/<Element cfg='30000' inputMode='1' hint='舒张压'>
										<Hint cfg='1000'>
											<Font color='808080' />舒张压</Hint>
									</Element>
									<Font color='0' cfg='0' />mmHg<Space count='2' />
									<Font cfg='1' />头围：<Font cfg='404' />
									<Element cfg='30000' hint='头围'>
										<Hint cfg='1000'>
											<Font color='808080' />头围</Hint>
									</Element>
									<Font color='0' cfg='0' />cm<Space count='2' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />前囟<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm（平、软）<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font size='0.397' color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />瞳孔<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />，哭声（响亮），神志（正常），抽搐（无），黄疸（无）。心</Paragraph>
								<Paragraph leftIndent='0.000' rightIndent='0.000'>
									<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />肺<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />腹<Font cfg='404' />
									<Element cfg='30000' hint='具体内容'>
										<Hint cfg='1000'>
											<Font color='808080' />具体内容</Hint>
									</Element>
									<Font color='0' cfg='0' />gan</Paragraph>
								<Paragraph>对光反射<Element cfg='30000' hint='是否灵敏'>
										<Hint cfg='1000'>
											<Font color='808080' />是否灵敏</Hint>
									</Element>
									<Font color='0' />；<Element cfg='30000' hint='结膜是否充血'>
										<Hint cfg='1000'>
											<Font color='808080' />结膜是否充血</Hint>
									</Element>
									<Font color='0' />，<Element cfg='30000' hint='左右眼'>
										<Hint cfg='1000'>
											<Font color='808080' />左右眼</Hint>
									</Element>
									<Font color='0' />分泌物<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='性质' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />性质</Hint>
									</Element>
									<Font size='0.388' color='0' />；耳廓发育<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />，外耳道分泌物<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='性质' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />性质</Hint>
									</Element>
									<Font size='0.388' color='0' />，外耳道畸形<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。鼻扇<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />，鼻外观畸形<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。口周青紫<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />，口唇<Element cfg='30000' hint='颜色'>
										<Hint cfg='1000'>
											<Font color='808080' />颜色</Hint>
									</Element>
									<Font color='0' />，唇裂<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />；口腔黏膜完整<Element cfg='30000' hint='是否'>
										<Hint cfg='1000'>
											<Font color='808080' />是否</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />；咽部：腭裂<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />颈部：<Font cfg='0' />抵抗感<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />，斜颈<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />，包块<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />胸部：<Font cfg='0' />胸廓外形<Element cfg='30000' hint='是否对称'>
										<Hint cfg='1000'>
											<Font color='808080' />是否对称</Hint>
									</Element>
									<Font color='0' />，乳房结节<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm，<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />肺部：<Font cfg='0' />呼吸运动<Element cfg='30000' hint='是否平稳'>
										<Hint cfg='1000'>
											<Font color='808080' />是否平稳</Hint>
									</Element>
									<Font color='0' />，呼吸音<Element cfg='30000' hint='是否清晰'>
										<Hint cfg='1000'>
											<Font color='808080' />是否清晰</Hint>
									</Element>
									<Font color='0' />，<Element cfg='30000' hint='是否对称'>
										<Hint cfg='1000'>
											<Font color='808080' />是否对称</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />，啰音：<Element cfg='30000' hint='是否存在'>
										<Hint cfg='1000'>
											<Font color='808080' />是否存在</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Font color='0' />粗湿啰音，部位<Space />
									</Element>。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />心脏：<Font cfg='0' />心前区<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />隆起，心尖搏动在胸骨左缘第<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />肋间，乳线<Element cfg='30000' hint='位置'>
										<Hint cfg='1000'>
											<Font color='808080' />位置</Hint>
									</Element>
									<Font color='0' cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm，震颤<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />，左心界最远处在乳线内<Element cfg='30000' hint='位置'>
										<Hint cfg='1000'>
											<Font color='808080' />位置</Hint>
									</Element>
									<Font color='0' cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm；心率<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />次/分，心音<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />，节律<Element cfg='30000' hint='是否整齐'>
										<Hint cfg='1000'>
											<Font color='808080' />是否整齐</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />，<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font size='0.397' color='808080' />有无</Hint>
									</Element>
									<Font size='0.388' color='0' />杂音<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />腹部：<Font cfg='0' />外形<Element cfg='30000' hint='是否平坦'>
										<Hint cfg='1000'>
											<Font size='0.397' color='808080' />是否平坦</Hint>
									</Element>
									<Font size='0.388' color='0' />
									<Element cfg='30000' hint='是否平坦'>平坦</Element>；腹壁皮肤<Element cfg='30000' hint='是否正常'>正常</Element>；脐部<Element cfg='30000' hint='是否干燥'>干燥</Element>，脐轮<Element cfg='30000' hint='有无'>无</Element>红肿，<Element cfg='30000' hint='有无'>无</Element>脓性分泌物，<Element cfg='30000' hint='有无'>无</Element>臭味；脐疝<Element cfg='30000' hint='有无'>无</Element>
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font size='0.397' color='ff' />，</BeforeTag>
										<Font color='0' cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />×<Font cfg='404' />
										<Element cfg='30000' hint='数值'>
											<Hint cfg='1000'>
												<Font color='808080' />数值</Hint>
										</Element>
										<Font color='0' cfg='0' />cm</Element>。<Element cfg='30000' hint='有无'>无</Element>胃肠型，<Element cfg='30000' hint='有无'>无</Element>蠕动波。腹肌<Element cfg='30000' hint='是否正常'>正常</Element>，肝右肋下<Font size='0.388' cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm，剑突下<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />cm，质地<Font cfg='404' />
									<Element cfg='30000' hint='如何'>正常</Element>
									<Font cfg='0' />；脾左肋下<Element cfg='30000' hint='可否扪及'>未扪及</Element>
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font size='0.397' color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />；<Element cfg='30000' hint='有无'>无</Element>包块，移动性浊音<Element cfg='30000' hint='有无'>无</Element>
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font size='0.397' color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />；肠鸣音<Font cfg='404' />
									<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' cfg='0' />次/分，音调<Element cfg='30000' hint='情况如何'>正常</Element>。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />脊柱四肢：<Font cfg='0' />
									<Element cfg='30000' hint='有无畸形'>
										<Hint cfg='1000'>
											<Font color='808080' />有无畸形</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />，四肢肌张力<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />。四肢温度<Element cfg='30000' hint='是否温暖'>
										<Hint cfg='1000'>
											<Font color='808080' />是否温暖</Hint>
									</Element>
									<Font color='0' />，毛细血管再充盈时间<Element cfg='30000' hint='数值'>
										<Hint cfg='1000'>
											<Font color='808080' />数值</Hint>
									</Element>
									<Font color='0' />秒。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />肛门及外生殖器：<Font cfg='0' />
									<Element cfg='30000' hint='有无'>有</Element>肛门，外生殖器外观：<Element cfg='30000' hint='女孩外生殖器'>正常。</Element>
									<Element cfg='30000' hint='男孩外生殖器'>尿道口下裂<Element cfg='30000' hint='尿道下裂'>无</Element>
										<Element cfg='30000' hint='有的具体情况' beforeTag='，'>
											<BeforeTag cfg='4'>
												<Font size='0.397' color='ff' />，</BeforeTag>
											<Hint cfg='1000'>
												<Font color='808080' />有的具体情况</Hint>
										</Element>
										<Font size='0.388' color='0' />，睾丸<Element cfg='30000' hint='位置如何'>
											<Hint cfg='1000'>
												<Font color='808080' />位置如何</Hint>
										</Element>
										<Font color='0' />。</Element>
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />神经系统：<Font cfg='0' />拥抱反射<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />，握持反射<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />，吸吮反射<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />，觅食反射<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />。围巾征<Element cfg='30000' hint='是否正常'>
										<Hint cfg='1000'>
											<Font color='808080' />是否正常</Hint>
									</Element>
									<Font color='0' />，<Element cfg='30000' hint='有无'>
										<Hint cfg='1000'>
											<Font color='808080' />有无</Hint>
									</Element>
									<Font color='0' />角弓反射体位。</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />其他：<Font cfg='0' />
									<Element cfg='30000' hint='有无其他情况'>
										<Hint cfg='1000'>
											<Font color='808080' />有无其他情况</Hint>
									</Element>
									<Font size='0.397' color='0' />
									<Element cfg='30000' hint='具体情况' beforeTag='，'>
										<BeforeTag cfg='4'>
											<Font color='ff' />，</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />具体情况</Hint>
									</Element>
									<Font size='0.388' color='0' />。</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
				<Paragraph leftIndent='0.000' rightIndent='0.000' spaceAfter='0.080'>
					<Table id='tableButton' rows='4' cols='1' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />门诊或外院检查资料：<Font cfg='0' />
									<Element name='辅助检查结果' cfg='30000' hint='请输入辅助检查'>
										<Hint cfg='1000'>
											<Font color='808080' />请输入辅助检查</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院初步诊断：<Font cfg='0' />
									<Element name='入院诊断' cfg='30000' hint='请输入入院诊断'>
										<Hint cfg='1000'>
											<Font color='808080' />请输入入院诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='28' />管床医师签名/工号：<Font cfg='404' />
									<Element name='ResidentDoctor' cfg='30000' hint='住院医生'>
										<Hint cfg='1000'>
											<Font color='808080' />住院医生</Hint>
									</Element>
									<Font color='0' cfg='0' />
									<Space />
									<Font cfg='404' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.450' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='32' />上级医师签名/工号：<Font cfg='404' />
									<Space count='8' />
									<Font cfg='0' />
									<Space />
									<Font cfg='404' />
									<Space count='5' />
									<Font cfg='0' />年<Font cfg='404' />
									<Space count='2' />
									<Font cfg='0' />月<Font cfg='404' />
									<Space count='2' />
									<Font cfg='0' />日<Font cfg='404' />
									<Space count='3' />
									<Font cfg='0' />时<Font cfg='404' />
									<Space count='2' />
									<Font cfg='0' />分</Paragraph>
							</Cell>
						</Row>
					</Table>
					<SelectionBegin side='1' />
					<SelectionEnd side='1' />
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
