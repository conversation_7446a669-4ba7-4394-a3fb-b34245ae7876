<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.397'  />
					<Space count='16' />
					<Font size='0.767' cfg='1' />死亡记录</Paragraph>
				<Paragraph>
					<Font size='0.370' cfg='0' />
					<Space count='40' />Death<Space />Note<Space />
				</Paragraph>
				<Paragraph>
					<Separator height='0.099' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住  院  科  室  名  称'>
						<Hint cfg='1000'>
							<Font color='808080' />住<Space count='2' />院<Space count='2' />科<Space count='2' />室<Space count='2' />名<Space count='2' />称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='4' />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />H01-06-YW-017<Space count='55' />第<PageNum width='0.580' height='0.469' lCfg='2'>
						<Unit width='0.580' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='2' />共<PageNum xCfg='1' width='0.851' height='0.469' lCfg='2'>
						<Unit width='0.851' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='52' />2014-03-A1</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='9' cols='4' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='6.758' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />入院时间：<Element name='PinDateSysTime' cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='6.818' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />死亡时间：<Element cfg='30000' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='3.849' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />住院天数：<Element cfg='30000' hint='几'>
										<Hint cfg='1000'>
											<Font color='808080' />几</Hint>
									</Element>
									<Font color='0' />天</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院诊断：<Font cfg='0' />
									<Element name='CCHPIIN' cfg='30000' hint='（促使患者就诊的最主要症状<或体征>及持续时间）'>
										<Hint cfg='1000'>
											<Font color='808080' />（促使患者就诊的最主要症状&lt;或体征&gt;及持续时间）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />病情摘要：<Font cfg='0' />
									<Element name='SOAPCurSymIN' cfg='30000' hint='（内容包括患者一般资料、主诉、简要病史、住院期间诊疗措施、病情演变等）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括患者一般资料、主诉、简要病史、住院期间诊疗措施、病情演变等）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />抢救经过：<Font cfg='0' />
									<Element cfg='30000' hint='(内容包括患者病情变化情况 <原因、诱因、症状、体征等>、抢救时间、抢救具体措施、抢救详细经过及治疗效果、参加抢救的医务人员姓名及专业技术职称等<抢救时间应当具体到分>。最后一次抢救应记录患者呼吸心跳停止的具体时间）'>
										<Hint cfg='1000'>
											<Font color='808080' />(内容包括患者病情变化情况<Space />&lt;原因、诱因、症状、体征等&gt;、抢救时间、抢救具体措施、抢救详细经过及治疗效果、参加抢救的医务人员姓名及专业技术职称等&lt;抢救时间应当具体到分&gt;。最后一次抢救应记录患者呼吸心跳停止的具体时间）</Hint>
									</Element>
									<Font color='0' />
									<Space count='2' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />最后诊断：<Font cfg='0' />
									<Element cfg='30000' hint='（最后诊断）'>
										<Hint cfg='1000'>
											<Font color='808080' />（最后诊断）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />死亡原因：<Font cfg='0' />
									<Element cfg='30000' hint='（死亡原因）'>
										<Hint cfg='1000'>
											<Font color='808080' />（死亡原因）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />医师签名/工号：<Font cfg='404' />
									<Space count='16' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />上级医生签名：<Font cfg='404' />
									<Space count='17' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.425' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='4'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='10' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分' beforeTag='时  间：'>
										<BeforeTag cfg='4'>
											<Font color='ff' />时间：</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
