<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='2.414' bottomPadding='1.962' leftPadding='1.962' rightPadding='1.962'>
			<Header>
				<Paragraph xCfg='12' lineSpaceValue='1.500'>
					<Font size='0.635' color='201f35'  cfg='1' />XXX医院</Paragraph>
				<Paragraph>
					<Font size='0.423' cfg='0' />
					<Element name='name' cfg='30000' hint='姓名' beforeTag='姓名:' width='2.502'>
						<BeforeTag cfg='4'>
							<Font color='ff' />姓名:</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='sickarea' cfg='30000' hint='病区' beforeTag='病区：' width='3.002'>
						<BeforeTag cfg='4'>
							<Font color='ff' />病区：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />病区</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='insickroom' cfg='30000' hint='床号' beforeTag='床号：' width='1.501'>
						<BeforeTag cfg='4'>
							<Font color='ff' />床号：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />床号</Hint>
					</Element>
					<Font color='201f35' />
					<Element name='Regno' cfg='30000' hint='住院号' beforeTag='住院号：' width='3.002'>
						<BeforeTag cfg='4'>
							<Font color='ff' />住院号：</BeforeTag>
						<Hint cfg='1000'>
							<Font color='808080' />住院号</Hint>
					</Element>
					<Font color='201f35' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font color='201f35'  />
				</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.503' color='201f35'  cfg='1' />
					<Space count='23' />
					<Font size='0.423' />
					<Space />
					<Font size='0.503' />
					<Space count='2' />
					<Font size='0.423' />
					<Space count='2' />
					<Font cfg='0' />阶段小结</Paragraph>
				<Paragraph>
					<Element name='CourseTime' cfg='30000' inputMode='3' timeType='1' hint='病程记录时间         '>
						<Hint cfg='1000'>
							<Font color='808080' />病程记录时间<Space count='9' />
						</Hint>
					</Element>
					<Font color='201f35' />
				</Paragraph>
				<Paragraph>
					<Font cfg='1' />
					<Space count='4' />
					<Font cfg='0' />患者<Element name='name' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='201f35' />，<Element name='gender' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='201f35' />，<Element name='Age' cfg='30000' hint='年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />年龄</Hint>
					</Element>
					<Font color='201f35' />岁，以“”为主诉于<Element name='indate' cfg='30000' inputMode='3' timeType='1' hint='入院时间his'>
						<Hint cfg='1000'>
							<Font color='808080' />入院时间his</Hint>
					</Element>
					<Font color='201f35' />
					<Element cfg='30000' hint='步行'>
						<Hint cfg='1000'>
							<Font color='808080' />步行</Hint>
					</Element>
					<Font color='201f35' />入院。入院查体：。入院诊断：辅助检查：。入院后先后予治疗，病情逐渐改善，目前无不适，目前，目前查体：目前诊断：，治疗同前。</Paragraph>
				<Paragraph>
					<Font cfg='1' />
					<Space count='63' />
					<Font cfg='0' />
					<Element name='LoginDoct' cfg='30000' hint='登录医生         '>
						<Hint cfg='1000'>
							<Font color='808080' />登录医生<Space count='9' />
						</Hint>
					</Element>
					<SelectionBegin side='1' />
					<SelectionEnd side='1' />
					<Font color='201f35' />
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
