<template>
  <el-aside :width="sidebarWidth" class="document-sidebar">
    <div class="sidebar-header">
      <el-icon :size="20"><Collection /></el-icon>
      <h2>病历列表</h2>
    </div>
    
    <!-- 顶部横向操作按钮 -->
    <div class="action-buttons">
      <el-button class="action-button"
        :type="activeMenu === 'create' ? 'primary' : 'default'"
        @click="setActiveMenu('create')"
      >
        <el-icon><DocumentAdd /></el-icon>
        <span>创建病历</span>
      </el-button>
      
      <el-button class="action-button"
        :type="activeMenu === 'submit' ? 'primary' : 'default'"
        @click="setActiveMenu('submit')">
        <el-icon><Promotion /></el-icon>
        <span>提交病历</span>
      </el-button>
      
      <el-button class="action-button"
        :type="activeMenu === 'print' ? 'primary' : 'default'"
        @click="setActiveMenu('print')">
        <el-icon><Printer /></el-icon>
        <span>整本打印</span>
      </el-button>
    </div>
    
    <!-- 纵向分类菜单 -->
    <el-menu 
      :default-active="activeMenu" 
      class="sidebar-menu" 
      text-color="#fff" 
      active-text-color="#fff" 
      @select="handleMenuSelect"
    >
      <!-- 动态生成分类菜单 -->
      <el-sub-menu 
        v-for="category in medicalUnitCategorys" 
        :key="category.id" 
        :index="category.typeid"
      >
        <template #title>
          <el-icon><Tickets /></el-icon>
          <span>{{ category.name }}</span>
          <span 
            v-if="getDocumentCount(category.typeid) > 0" 
            class="badge badge-primary"
          >
            {{ getDocumentCount(category.typeid) }}
          </span>
        </template>
        
        <!-- 生成该分类下的文档列表 -->
        <el-menu-item 
          v-for="doc in getDocumentsByCategory(category.typeid)" 
          :key="doc.id" 
          :index="`doc-${doc.id}`"
          @dblclick="$emit('item-dblclick', doc)"
        >
          <el-popover
          placement="right"
          trigger="hover" 
          width="300"
          >
                    <!-- :visible="hoverDocId === doc.id" -->
            <template #reference>
              <div style="display: flex; align-items: center; width: 100%;">
              <!-- <div @mouseenter="hoverDocId = doc.id" @mouseleave="hoverDocId = null" style="display: flex; align-items: center; width: 100%;"> -->
              <el-icon><Document /></el-icon>
              <span>{{ doc.name + ' ' + formatDate(doc.created) }}</span>
              <span class="badge" :class="getBadgeClass(doc.dirid)">
                {{ doc.order }}
              </span>
              </div>
            </template>
            <!-- 自定义 Popover 内容 -->
            <div>
              <h4>{{ doc.name }}</h4>
              <p>ID: {{ doc.id }}</p>
              <p>创建时间: {{ doc.created }}</p>
              <p>创建人: {{ doc.lastmodifiedname }}</p>
              <p>最后修改时间: {{ doc.lastmodified }}</p>
              <p>最后修改人: {{ doc.lastmodifiedname }}</p>
              <p>文档状态: {{ doc.status }}</p>
            </div>
          </el-popover>
          <!-- <el-icon><Document /></el-icon>
          <span>{{ doc.name +' '+ formatDate(doc.created) }}</span>
          <span class="badge" :class="getBadgeClass(doc.dirid)">
            {{ doc.order }}
          </span> -->
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </el-aside>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  FolderOpened,
  DocumentAdd,
  Promotion,
  Printer,
  Tickets,
  Document,
  Collection
} from '@element-plus/icons-vue'

import dayjs from 'dayjs';



const props = defineProps({
  width: {
    type: String,
    default: '300px'
  },
  unitcategorys: {
    type: Array,
    default: () => []
  },
  documents: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['menu-change','item-dblclick'])

const activeMenu = ref('admission')

// 修改双击事件处理
const handleItemDoubleClick = (doc) => {
  emit('item-dblclick', doc)
}

// 计算属性
const sidebarWidth = computed(() => props.width)

function formatDate(dateStr) {
  const date = new Date(dateStr)
  return dayjs(date).format('YYYY-MM-DD')
}
// 计算属性：按order字段排序后的分类
const medicalUnitCategorys = computed(() => {
  return [...props.unitcategorys].sort((a, b) => a.order - b.order);
})


const medicalDocuments = computed(() => {
  return [...props.documents].sort((a, b) => a.order - b.order);
})

// 方法：获取指定分类的文档数量
const getDocumentCount = (categoryCode) => {
  return medicalDocuments.value.filter(doc => doc.dirid === categoryCode).length
}


// 方法：获取指定分类的文档列表
const getDocumentsByCategory = (categoryCode) => {
  return medicalDocuments.value
    .filter(doc => doc.dirid === categoryCode)
    .sort((a, b) => a.sno - b.sno) // 按序号排序
}

// 方法：获取徽章样式类
const getBadgeClass = (dirid) => {
  switch(dirid) {
    case '1': return 'badge-primary'
    case '1': return 'badge-warning'
    case '2': return 'badge-success'
    default: return 'badge-primary'
  }
}


const hasNewAuthorization = computed(() => {
  return props.documents.some(doc => 
    doc.category === 'authorization' && 
    new Date(doc.created) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  )
})

// 方法
const handleMenuSelect = (index) => {
  activeMenu.value = index
  emit('menu-change', index)
}

const setActiveMenu = (menu) => {
  activeMenu.value = menu
  emit('menu-change', menu)
}
</script>

<style scoped>
.document-sidebar {
  background-color: var(--el-color-primary);
  /* background-color: var(--sidebar-bg); */
  color: white;
  height: 100%;
  overflow-y: auto;
  transition: all 0.3s ease;
  box-shadow: 2px 0 8px rgba(0,0,0,0.15);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
}

.sidebar-header h2 {
  margin-left: 10px;
  font-size: 1.3rem;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  padding: 1px;
  gap: 2px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: auto;
  padding: 10px 1px;
}

.action-button :deep(.el-icon) {
  margin-right: 0;
  margin-bottom: 4px;
  font-size: 16px;
}

.action-button span {
  font-size: 12px;
  line-height: 1.2;
}

.sidebar-menu {
  background-color: var(--el-color-primary);
  /* background-color: var(--sidebar-bg); */
  border-right: none;
  flex: 1;
  overflow-y: auto;
}

:deep(.el-menu){
  background-color: transparent;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: #1e88e5 !important;
}

:deep(.el-menu-item.is-active::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: white;
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: #34495e !important;
}

:deep(.el-sub-menu .el-menu-item) {
  padding-left: 20px !important;
  background-color: rgba(0,0,0,0.15) !important;
}

:deep(.el-sub-menu .el-menu-item:hover) {
  background-color: rgba(0,0,0,0.25) !important;
}

:deep(.el-icon) {
  margin-right: 12px;
  width: 20px;
  text-align: left;
  font-size: 18px;
}

.badge {
  padding: 1px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 16px;  /* 添加固定的行高 */
  height: 16px;  /* 固定高度 */
  margin-left: 8px;
}

.badge-primary {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.badge-success {
  background-color: #e8f5e9;
  color: #4caf50;
}

.badge-warning {
  background-color: #fff8e1;
  color: #ff9800;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-sidebar {
    width: 100% !important;
    height: auto;
    max-height: 300px;
  }
  
  .sidebar-header h2 {
    display: none;
  }
  
  .action-buttons {
    flex-direction: row;
  }
  
  .action-button {
    padding: 5px;
  }
  
  .action-button span {
    display: none;
  }
  
  :deep(.el-menu-item span),
  :deep(.el-sub-menu__title span) {
    display: none;
  }
  
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    justify-content: center;
    padding: 0 !important;
  }
  
  :deep(.el-icon) {
    margin-right: 0;
    font-size: 1.2rem;
  }
  
  :deep(.el-sub-menu .el-menu-item) {
    padding-left: 0 !important;
    justify-content: center;
  }
  
  .badge {
    display: none;
  }
}
</style>