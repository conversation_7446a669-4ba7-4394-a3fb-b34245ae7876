<template>
  <div class="document-manager-container">
    <!-- 病历列表 -->
    <DocumentSidebar :documents="documents" :unitcategorys="unitcategorys" @menu-change="handleMenuChange"
      @item-dblclick="handleItemDoubleClick" />
    <el-main>
      <div class="Center-box">
        <div class="emr-center">
          <div class="emr-center-box">
            <section class="center-editor">
              <!-- <el-button size="small" type="primary" @click="openDoc">打开文档</el-button>
              <el-button size="small" type="primary" @click="newDoc">新建文档</el-button>
              <el-button size="small" type="primary" @click="replaceDoc">替换文档</el-button> -->
              <DocumentHead />
              <div class="Instance">
                <div @contextmenu.prevent="handleContextMenu" class="main-box" style="width: 1100px; height: 900px">
                  <el-tabs style="width: 100%; height: 100%" :closable="true" v-model="selectedDocName" type="card"
                    @tab-click="tabSelected" @tab-remove="tabRemove" @tab-add="tabAdd">
                    <el-tab-pane style="width: 100%; height: 900px" v-for="item in bindEditors" :key="item.docName"
                      :label="item.showName" :name="item.docName">
                      <component :is="ThinkEditorView" :thinkEditor="item" />
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script setup>
import { ref, triggerRef } from 'vue'
import { useRoute } from 'vue-router'
import { ThinkEditorInstance } from '@/components/editor/ThinkEditor.Instance'
import { GetRandStr } from '@/components/editor/ThinkEditor.Utils'
import {
  E_LOG_LEVEL,
  ThinkEditorEvent,
  E_VIEW_MODE,
  E_DOC_TYPE,
  E_EVENT_KEY,
  E_DOCS_ORGANIZE_MODE,
} from '@/components/editor/ThinkEditor.Defined'
import ThinkEditorView from './ThinkEditorView.vue'
import DocumentSidebar from './DocumentSidebar.vue'
import DocumentHead from './DocumentHead.vue'
import { ThinkEditor } from '@/components/editor/ThinkEditor'

// 响应式状态
const selectedDocName = ref('')
const bindEditors = ref([])
const activeMenu = ref('admission')
const editorsInstance = new ThinkEditorInstance('实例1')

// 初始化 bindEditors 为 editorsInstance.editors
bindEditors.value = editorsInstance.editors

// 科室分类数据
const unitcategorys = ref([
  {
    id: 1,
    unit: '外科',
    typeid: '1',
    order: 1,
    name: '入院记录'
  },
  {
    id: 2,
    unit: '外科',
    typeid: '2',
    order: 2,
    name: '病程记录'
  },
  {
    id: 4,
    unit: '外科',
    typeid: '4',
    order: 4,
    name: '知情同意书'
  },
  {
    id: 3,
    unit: '外科',
    typeid: '117',
    order: 3,
    name: '需另页书写的文档'
  },
  {
    id: 5,
    unit: '外科',
    typeid: '191',
    order: 5,
    name: '出院记录'
  }
])

// 文档列表数据
const documents = ref([
  {
    id: 1,
    order: 1,
    name: '外科入院记录',
    dirid: '1',
    createddocuid: 'admin',
    createddocname: 'admin',
    created: '2024-03-21 16:24:08',
    lastmodifieduid: 'admin',
    lastmodifiedname: 'admin',
    lastmodified: '2024-03-22 09:15:33',
    peopleunitid: '1',
    peopleunitname: '外科',
    peoplebingqu: '外科一病区',
    peoplechuanghao: '1',
    peoplenianling: '33',
    truetype: '33',
    roottype: '1',
    status: '已完成'
  },
  {
    id: 2,
    order: 2,
    name: '外科入院记录',
    dirid: '1',
    createddocuid: 'admin',
    createddocname: 'admin',
    created: '2024-03-22 16:24:08',
    lastmodifieduid: 'admin',
    lastmodifiedname: 'admin',
    lastmodified: '2024-03-23 09:15:33',
    peopleunitid: '1',
    peopleunitname: '外科',
    peoplebingqu: '外科一病区',
    peoplechuanghao: '1',
    peoplenianling: '33',
    truetype: '33',
    roottype: '1',
    status: '未完成'
  },
  {
    id: 3,
    order: 1,
    name: '首次病程记录',
    dirid: '2',
    createddocuid: 'admin',
    createddocname: 'admin',
    created: '2024-03-21 6:24:08',
    lastmodifieduid: 'admin',
    lastmodifiedname: 'admin',
    lastmodified: '2024-03-21 09:15:33',
    peopleunitid: '1',
    peopleunitname: '外科',
    peoplebingqu: '外科一病区',
    peoplechuanghao: '1',
    peoplenianling: '33',
    truetype: '2',
    roottype: '2',
    status: '已打印'
  }
])

// 事件处理函数
const handleContextMenu = () => {
  // 禁用编辑器区域的默认右键菜单
}

const tabSelected = (tab) => {
  selectDocEditor(tab.paneName)
}

const tabRemove = (name) => {
  const docName = name
  selectedDocName.value = docName
  closeDocEditor(docName)
}

const tabAdd = () => { }

const handleMenuChange = (menu) => {
  activeMenu.value = menu
  // 根据菜单切换内容
}

// 文档操作函数
const fetchDocData = async (path) => {
  try {
    const response = await fetch(`/static/system/emr/${path}`)
    return await response.arrayBuffer()
  } catch (error) {
    console.error('获取文档数据失败:', error)
    return null
  }
}

const OpenDoc = async (docName, docType, docData, options = {}, createNew = false) => {
  let existEditor = editorsInstance.GetEditor(docName)
  
  if (existEditor === undefined || createNew) {
    const thinkEditor = await createDocEditor(docName)
    if (thinkEditor !== undefined) {
      if (docData) {
        thinkEditor.CreateDoc(docName)
        thinkEditor.ParseDoc(docName, docData, {
          organizeMode: E_DOCS_ORGANIZE_MODE.Single,
          ...options
        })
        thinkEditor.SetViewMode(E_VIEW_MODE.Edit)
      } else {
        thinkEditor.NewDoc(docName, docType)
        thinkEditor.SetViewMode(E_VIEW_MODE.Edit)
      }
      existEditor = thinkEditor
    }
  }
  
  selectDocEditor(docName)
  return existEditor
}

const openDocTest = async () => {
  const docName = '演示文档'
  const docData = await fetch(
    '/static/system/emr/演示/00-演示病例/AdmissionRecord.xml'
  ).then((res) => res.arrayBuffer())
  
  return await OpenDoc(docName, E_DOC_TYPE.Entity, docData)
}

const onDocOpt = async (opt) => {
  switch (opt) {
    case 'openDoc': {
      await openDocTest();
      break;
    }
    case 'newEntityDoc': {
      let docData = await fetchDocData('空模版/empty.xml');
      var docName = 'new' + GetRandStr();
      return await OpenDoc(docName, E_DOC_TYPE.Entity, docData, {}, true);
    }
    case 'newTemplateDoc': {
      let docData = await fetchDocData('空模版/empty.xml');
      var docName = 'new' + GetRandStr();
      return await OpenDoc(docName, E_DOC_TYPE.Template, docData, {}, true);
    }
    case 'newTimeAxisDoc': {
      let docData = await fetchDocData('空模版/emptyTimeAxis.xml');
      var docName = 'new' + GetRandStr();
      return await OpenDoc(docName, E_DOC_TYPE.Template, docData, {}, true);
    }
  }
}

const handleItemDoubleClick = async (doc) => {
  const docName = doc.name;
  let existEditor = editorsInstance.GetEditor(docName)

  if (existEditor === undefined) {
    const thinkEditor = await createDocEditor(docName)
    if (thinkEditor !== undefined) {
      const docData = await fetch(
        '/static/system/emr/演示/00-演示病例/AdmissionRecord.xml'
      ).then((res) => res.arrayBuffer())

      thinkEditor.CreateDoc(docName)
      thinkEditor.ParseDoc(docName, docData, {
        organizeMode: E_DOCS_ORGANIZE_MODE.Single,
      })
      thinkEditor.SetViewMode(E_VIEW_MODE.Edit)
    }
    existEditor = thinkEditor
  }

  selectDocEditor(docName)
}

const openDoc = async () => {
  const docName = '演示文档'
  let existEditor = editorsInstance.GetEditor(docName)

  if (existEditor === undefined) {
    const thinkEditor = await createDocEditor(docName)
    if (thinkEditor !== undefined) {
      const docData = await fetch(
        '/static/system/emr/演示/00-演示病例/AdmissionRecord.xml'
      ).then((res) => res.arrayBuffer())

      thinkEditor.CreateDoc(docName)
      thinkEditor.ParseDoc(docName, docData, {
        organizeMode: E_DOCS_ORGANIZE_MODE.Single,
      })
      thinkEditor.SetViewMode(E_VIEW_MODE.Edit)
    }
    existEditor = thinkEditor
  }

  selectDocEditor(docName)
}

const newDoc = async () => {
  const docName = '文档' + GetRandStr()
  const thinkEditor = await createDocEditor(docName)
  if (thinkEditor !== undefined) {
    thinkEditor.NewDoc(docName, E_DOC_TYPE.Entity)
  }
  selectDocEditor(docName)
  thinkEditor.SetViewMode(E_VIEW_MODE.Edit)
  return thinkEditor
}

const replaceDoc = async () => {
  editorsInstance.CloseEditor('演示文档')
  return await openDoc()
}

const createDocEditor = async (docName) => {
  const editorParam = {
    lib: '/static/editor/',
    fontPath: '/static/editor_fonts/',
    docName: docName,
    useInnerUI: true,
    useInnerMenuBox: true,
  }

  const thinkEditor = new ThinkEditor(editorParam)
  await thinkEditor.Init()
  editorsInstance.AddEditor(thinkEditor)
  return thinkEditor
}

const selectDocEditor = (docName) => {
  editorsInstance.SelectEditor(docName)
  updateDocsTabsView()
}

const updateDocsTabsView = () => {
  const selecedEditor = editorsInstance.GetSelectedEditor()
  console.log(selecedEditor)
  selectedDocName.value = selecedEditor ? selecedEditor.docName : ''
}

const closeDocEditor = (docName) => {
  const closeEditor = editorsInstance.GetEditor(docName)
  if (closeEditor == undefined) {
    console.error('要关闭的文档不存在：', docName)
    return
  }
  editorsInstance.CloseEditor(closeEditor.docName)
  updateDocsTabsView()
}

// 事件监听函数
const onInstanceChange = (evt) => {
  triggerRef(bindEditors)
}

const onOpenMenu = (evt) => {
  const e = evt
  const thinkEditor = e.data.editor
  const openMenuData = e.data.data
}

const onDocModified = (evt) => {
  const e = evt
  const thinkEditor = e.data.editor
  thinkEditor.showName = thinkEditor.docName + '*'
  editorsInstance.CreateInstanceChangeEvent()
}

const onFontFormatChange = (evt) => {
  const e = evt
  const data = e.data
  console.log(data)
}

const onDocEditorFocusChange = (evt) => {
  const e = evt
  const data = e.data
  console.log(data)
}

const onTimeStamp = (evt) => {
  const e = evt
  const data = e.data
  console.log('=====>OnTimeStamp:' + data)
}

// 注册事件监听器
editorsInstance.addEventListener(E_EVENT_KEY.timeStamp, onTimeStamp)
editorsInstance.addEventListener(E_EVENT_KEY.instanceChange, onInstanceChange)
editorsInstance.addEventListener(E_EVENT_KEY.fontFormat, onFontFormatChange)
editorsInstance.addEventListener(E_EVENT_KEY.editorFocus, onDocEditorFocusChange)
editorsInstance.addEventListener(E_EVENT_KEY.docModified, onDocModified)
editorsInstance.addEventListener(E_EVENT_KEY.openMenu, onOpenMenu)
</script>

<style scoped>
.document-manager-container {
  display: flex;
  height: 100%;
}

.Center-box {
  width: 100%;
  height: 100%;
}

.emr-center {
  width: 100%;
  height: 100%;
}

.emr-center-box {
  width: 100%;
  height: 100%;
}

.center-editor {
  width: 100%;
  height: 100%;
}

.Instance {
  width: 100%;
  height: 100%;
}

.main-box {
  width: 100%;
  height: 100%;
}
</style>