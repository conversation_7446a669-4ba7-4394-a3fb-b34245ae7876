<?xml version='1.0' encoding='utf-8'?>
<Doc type='entity' pagesCount='1'>
	<Sections>
		<Section cfg='1' pagesCount='0' width='21.008' height='29.713' topPadding='1.182' bottomPadding='0.981' leftPadding='2.465' rightPadding='0.981'>
			<Header>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.397'  />
					<Space count='9' />
					<Font size='0.767' cfg='1' />24小时内入院死亡记录</Paragraph>
				<Paragraph>
					<Font size='0.397' cfg='0' />
					<Space count='12' />
					<Font size='0.370' cfg='1' />Death<Space />Note<Space />for<Space />Patient<Space />Admitted<Space />Within<Space />24<Space />Hours</Paragraph>
				<Paragraph>
					<Separator height='0.123' lineHeight='0.040' lCfg='5' />
					<Font size='0.423' cfg='0' />姓名：<Font cfg='404' />
					<Element name='EMRName' cfg='30000' hint='姓名'>
						<Hint cfg='1000'>
							<Font color='808080' />姓名</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />性别：<Font cfg='404' />
					<Element name='EMRSexType' cfg='30000' hint='性别'>
						<Hint cfg='1000'>
							<Font color='808080' />性别</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />年龄:<Space />
					<Font cfg='404' />
					<Element name='complex_NianLin' cfg='30000' hint='复杂年龄'>
						<Hint cfg='1000'>
							<Font color='808080' />复杂年龄</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='2' />出生日期：<Font cfg='404' />
					<Element name='EMRDateBirth' cfg='30000' hint='出生日期'>
						<Hint cfg='1000'>
							<Font color='808080' />出生日期</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
				<Paragraph>
					<Font cfg='0' />科别：<Font cfg='404' />
					<Element name='T2HcDeptName' cfg='30000' hint='住  院  科  室  名  称'>
						<Hint cfg='1000'>
							<Font color='808080' />住<Space count='2' />院<Space count='2' />科<Space count='2' />室<Space count='2' />名<Space count='2' />称</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />床号：<Font cfg='404' />
					<Element name='PINBedNbrFirst' cfg='30000' hint='病床号'>
						<Hint cfg='1000'>
							<Font color='808080' />病床号</Hint>
					</Element>
					<Font color='0' cfg='0' />
					<Space count='3' />健康档案号：<Font cfg='404' />
					<Element name='EMRId' cfg='30000' hint='健康档案号'>
						<Hint cfg='1000'>
							<Font color='808080' />健康档案号</Hint>
					</Element>
					<Font color='0' />
				</Paragraph>
			</Header>
			<Footer>
				<Paragraph xCfg='10' lineSpaceValue='1.500'>
					<Font size='0.370'  />H01-06-YW-016<Space count='55' />第<PageNum width='0.580' height='0.469' lCfg='2'>
						<Unit width='0.580' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='2' />共<PageNum xCfg='1' width='0.851' height='0.469' lCfg='2'>
						<Unit width='0.851' height='0.469'>
							<Paragraph xCfg='2' />
						</Unit>
					</PageNum>
					<Font size='0.370'  />页<Space count='52' />2014-03-A1</Paragraph>
			</Footer>
			<Body>
				<Paragraph xCfg='10' spaceAfter='0.080' lineSpaceValue='1.500'>
					<Table id='MainGrid' rows='16' cols='3' padding='0.012'>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='80' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />出<Space />生<Space />地：<Font cfg='0' />
									<Element name='EMRCategory' cfg='30000' hint='籍贯'>
										<Hint cfg='1000'>
											<Font color='808080' />籍贯</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='80' width='4.356' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />民<Space count='3' />族：<Font cfg='0' />
									<Element name='T2FolkDesc' cfg='30000' hint='民族名称'>
										<Hint cfg='1000'>
											<Font color='808080' />民族名称</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' eCfg='80' width='4.356' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />职业：<Font cfg='0' />
									<Element name='CareerCode' cfg='30000' hint='职业'>
										<Hint cfg='1000'>
											<Font color='808080' />职业</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />婚<Space count='4' />姻：<Font cfg='0' />
									<Element cfg='30000' hint='婚姻'>
										<Hint cfg='1000'>
											<Font color='808080' />婚姻</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />住<Space count='3' />址：<Font cfg='0' />
									<Element name='EMRContactFlagAddr' cfg='30000' hint='同联系地址'>
										<Hint cfg='1000'>
											<Font color='808080' />同联系地址</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.687'>
							<Cell xCfg='1' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />联系电话：<Font cfg='0' />
									<Element name='EMRTelMobile' cfg='30000' hint='移动电话'>
										<Hint cfg='1000'>
											<Font color='808080' />移动电话</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />电子邮件（E-Mail)<Font cfg='0' />：<Element name='EMREmail' cfg='30000' hint='电子邮件'>
										<Hint cfg='1000'>
											<Font color='808080' />电子邮件</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院时间：<Font cfg='0' />
									<Element name='PinDateSysTime' cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日 HH时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日<Space />HH时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />问诊时间：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />死亡时间：<Font cfg='0' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分'>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />过敏药物：<Font cfg='0' />
									<Element name='EMRDrugAllergicIN' cfg='30000' hint='双击选择过敏药物'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择过敏药物</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />过敏食物：<Font cfg='0' />
									<Element name='EMRDrugAllergicIN' cfg='30000' hint='双击选择过敏食物'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择过敏食物</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院前用药史：<Font cfg='0' />
									<Element name='HOBookTakeDrugRecent' cfg='30000' hint='双击编辑，目前用药情况'>
										<Hint cfg='1000'>
											<Font color='808080' />双击编辑，目前用药情况</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='8.721' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院方式：<Font cfg='0' />
									<Element cfg='30000' hint='入院方式'>
										<Hint cfg='1000'>
											<Font color='808080' />入院方式</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
							<Cell xCfg='1' width='8.705' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='2'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />病史陈述者：<Font cfg='0' />
									<Element cfg='30000' hint='病史陈述者姓名'>
										<Hint cfg='1000'>
											<Font color='808080' />病史陈述者姓名</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />主<Space count='4' />诉：<Font cfg='0' />
									<Element name='CCHPIIN' cfg='30000' hint='（促使患者就诊的最主要症状<或体征>及持续时间）'>
										<Hint cfg='1000'>
											<Font color='808080' />（促使患者就诊的最主要症状&lt;或体征&gt;及持续时间）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院时情况：<Font cfg='0' />
									<Element name='SOAPCurSymIN' cfg='30000' hint='（内容包括入院原因、主诉、病情、体查发现，主要辅助检查结果等）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括入院原因、主诉、病情、体查发现，主要辅助检查结果等）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />入院时诊断：<Font cfg='0' />
									<Element cfg='30000' hint='双击选择入院诊断'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择入院诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />诊疗经过（抢救经过）、死亡原因：<Font cfg='0' />
									<Element cfg='30000' hint='（内容包括住院期间诊疗过程效果、重要的药品治疗（含药物名称、剂量、使用频次、途径等）、已施行的诊断性和治疗性操作；有手术治疗的，应记录手术方式、术后治疗方式及效果、病理切片结果等；有抢救的，应记录病情变化情况、抢救时间及措施、参加抢救的医务人员姓名及专业技术职称、患者呼吸心跳停止具体时间；患者死亡原因分析）'>
										<Hint cfg='1000'>
											<Font color='808080' />（内容包括住院期间诊疗过程效果、重要的药品治疗（含药物名称、剂量、使用频次、途径等）、已施行的诊断性和治疗性操作；有手术治疗的，应记录手术方式、术后治疗方式及效果、病理切片结果等；有抢救的，应记录病情变化情况、抢救时间及措施、参加抢救的医务人员姓名及专业技术职称、患者呼吸心跳停止具体时间；患者死亡原因分析）</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='10' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  cfg='1' />死亡诊断：<Font cfg='0' />
									<Element cfg='30000' hint='双击选择死亡诊断'>
										<Hint cfg='1000'>
											<Font color='808080' />双击选择死亡诊断</Hint>
									</Element>
									<Font color='0' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />医师签名/工号：<Space count='16' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />上级医生签名：<Space count='17' />
								</Paragraph>
							</Cell>
						</Row>
						<Row height='0.000' xCfg='1'>
							<Cell xCfg='1' eCfg='1' width='17.422' topPadding='0.051' bottomPadding='0.051' leftPadding='0.094' rightPadding='0.094' colSpan='3'>
								<Paragraph xCfg='14' leftIndent='0.020' rightIndent='0.020' lineSpaceValue='1.500'>
									<Font size='0.388'  />
									<Space count='10' />
									<Element cfg='30000' inputMode='3' timeType='2' hint='yyyy年MM月dd日hh时mm分' beforeTag='时  间：'>
										<BeforeTag cfg='4'>
											<Font color='ff' />时间：</BeforeTag>
										<Hint cfg='1000'>
											<Font color='808080' />yyyy年MM月dd日hh时mm分</Hint>
									</Element>
									<Font color='0' />
									<Space />
								</Paragraph>
							</Cell>
						</Row>
					</Table>
				</Paragraph>
			</Body>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources>
		<Editors />
		<OptionRes />
		<Algorithms />
		<ImageRes />
	</Resources>
</Doc>
