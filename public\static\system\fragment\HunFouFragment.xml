<?xml version="1.0" encoding="utf-8"?>
<Doc type="fragment" pagescount="1">
	<Sections>
		<Section cfg="1" pagescount="1" width="21" height="30" border-width="0.02" top-padding="2.5" bottom-padding="2.5" left-padding="3.1" right-padding="3.1">
			<Attach back-color="ffffff" width="2" border-width="0.02" />
			<Header top-margin="0.2" top-border-width="0.02" bottom-border-width="0.1" left-border-width="0.02" right-border-width="0.02">
				<Paragraph space-after="0.1" linespace-value="0.6" />
			</Header>
			<Footer border-width="0.02" top-border-style="1">
				<Paragraph linespace-value="0.6" />
			</Footer>
			<Main border-width="0.02">
				<Paragraph id="1" name="11">
					<Element cfg="30000" input-mode="2" option-class="系统" option-id="婚姻状况" items="已婚" hint="请选择">
						<Expression action="this.element.value=='未婚' ?married.element.visible=false:married.element.visible=true" />已婚</Element>。<Element id="married" cfg="30000" hint="请输入">
						<Space />结婚年龄：<Element cfg="30000" hint="年龄">
							<Hint cfg="1000">
								<Format color="808080" />年龄</Hint>
						</Element>
						<Format color="0" />
						<Space />岁，配偶健康<Element cfg="30000" input-mode="2" option-class="系统" option-id="良好程度" hint="请选择">
							<Hint cfg="1000">
								<Format color="808080" />请选择</Hint>
						</Element>
						<Format color="0" />
						<Space />，<Element cfg="30000" input-mode="2" option-class="系统" option-id="生育情况" items="无子女" hint="请选择">无子女</Element>。<Space />
					</Element>
					<Space />
				</Paragraph>
			</Main>
			<InfoBoxs />
		</Section>
	</Sections>
	<Resources />
</Doc>
