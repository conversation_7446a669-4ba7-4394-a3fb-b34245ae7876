<template>
  <div ref="editorContainer" style="width: 100%; height: 100%"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  thinkEditor: {
    type: Object,
    default: null
  }
})

const editorContainer = ref(null)

// Load editor when component mounts or when thinkEditor changes
const loadEditor = () => {
  if (props.thinkEditor && editorContainer.value) {
    props.thinkEditor.Load(editorContainer.value)
  }
}

// Cleanup when component unmounts
const cleanup = () => {
  if (props.thinkEditor) {
    props.thinkEditor.UnLoad()
  }
}

onMounted(() => {
  loadEditor()
})

onBeforeUnmount(() => {
  cleanup()
})

// Watch for changes in thinkEditor prop
watch(() => props.thinkEditor, (newEditor, oldEditor) => {
  if (oldEditor) {
    oldEditor.UnLoad()
  }
  if (newEditor && editorContainer.value) {
    newEditor.Load(editorContainer.value)
  }
}, { immediate: true })
</script>
